# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 实体合并配置文件
"""

# LLM配置
LLM_CONFIG = {
    "base_url": "https://open.bigmodel.cn/api/paas/v4",
    "api_key": "fdd92b30af6c46efb5d18e0eb6540816.c69FP2JZnAAMRzia",
    "model_name": "glm-4-flash",
    "temperature": 0.1
}

# BERT模型配置
BERT_CONFIG = {
    "model_path": "../bert-base-chinese",
    "max_length": 64,
    "batch_size": 16
}

# 实体合并配置
MERGE_CONFIG = {
    "similarity_threshold": 0.9,  # 相似度阈值
    "max_iterations": 1,  # 最大迭代轮数
    "max_candidates_per_iteration": 50,  # 每轮最大候选对数
    "batch_size": 10,  # LLM批处理大小
    "progress_save_interval": 1  # 进度保存间隔（轮数）
}

# 文件路径配置
FILE_CONFIG = {
    "input_file": "../data/standardized_att_re/glm-4-flash/glm-4-flash_standardized.jsonl",
    "output_dir": "../data/merged/glm-4-flash/",
    "progress_dir": "../data/merged/progress",
    "backup_dir": "../data/merged/backups"
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "./logs/entity_merge.log"
}

# 移除实体类型限制，允许所有实体类型合并
ENTITY_TYPE_MAPPING = {}

# 移除合并规则限制，允许所有实体类型合并
MERGE_RULES = {}

# 性能配置
PERFORMANCE_CONFIG = {
    "use_gpu": True,  # 是否使用GPU
    "max_workers": 4,  # 最大工作线程数
    "memory_limit": "8GB",  # 内存限制
    "timeout": 300,  # 超时时间（秒）
    "retry_times": 3  # 重试次数
} 