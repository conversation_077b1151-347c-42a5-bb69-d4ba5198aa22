2025-08-01 11:03:15,680 - __main__ - INFO - ============================================================
2025-08-01 11:03:15,681 - __main__ - INFO - 实体合并工具启动
2025-08-01 11:03:15,681 - __main__ - INFO - ============================================================
2025-08-01 11:03:15,681 - __main__ - INFO - 验证输入文件: ../data/standardized_att_re/glm-4-flash/glm-4-flash_standardized.jsonl
2025-08-01 11:03:15,683 - __main__ - INFO - 创建输出目录: ../data/merged/glm-4-flash/
2025-08-01 11:03:15,688 - __main__ - INFO - 初始化实体合并器
2025-08-01 11:03:19,027 - __main__ - INFO - 开始执行实体合并
2025-08-01 11:03:19,027 - __main__ - ERROR - 执行过程中发生错误: EntityMerger.merge_entities() got an unexpected keyword argument 'resume'
2025-08-01 11:03:19,036 - __main__ - ERROR - 详细错误信息:
Traceback (most recent call last):
  File "E:\code\work\pythonProject\fish\A-20250802-llms-tripleExtraction-eva\entity_merger\run_entity_merge.py", line 150, in main
    result = merger.merge_entities(
             ^^^^^^^^^^^^^^^^^^^^^^
TypeError: EntityMerger.merge_entities() got an unexpected keyword argument 'resume'
