# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 数据更新模块，处理合并后的实体数据更新
"""

import json
from typing import Dict, List, Set
from datetime import datetime
import os
from tqdm import tqdm

class DataUpdater:
    """数据更新器"""
    
    def __init__(self):
        """初始化数据更新器"""
        pass
    
    def extract_entities_from_jsonl(self, file_path: str) -> Dict[str, str]:
        """
        从JSONL文件中提取所有实体及其类型
        
        Args:
            file_path: JSONL文件路径
            
        Returns:
            实体名称到类型的映射字典
        """
        entity_types = {}
        
        print(f"正在从 {file_path} 提取实体信息...")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    
                    # 提取实体属性中的实体
                    if 'entity_attribute' in data:
                        for entity in data['entity_attribute']:
                            if 'name' in entity and 'type' in entity:
                                entity_name = entity['name']
                                entity_type = entity['type']
                                entity_types[entity_name] = entity_type
                    
                    # 提取实体关系中的实体
                    if 'entity_relation' in data:
                        for relation in data['entity_relation']:
                            if 'source_entity' in relation and 'source_entity_type' in relation:
                                source_entity = relation['source_entity']
                                source_type = relation['source_entity_type']
                                entity_types[source_entity] = source_type
                            
                            if 'target_entity' in relation and 'target_entity_type' in relation:
                                target_entity = relation['target_entity']
                                target_type = relation['target_entity_type']
                                entity_types[target_entity] = target_type
                                
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    continue
        
        print(f"提取到 {len(entity_types)} 个实体")
        return entity_types
    
    def update_entity_names(self, data: Dict, merge_mapping: Dict[str, str]) -> Dict:
        """
        更新数据中的实体名称
        
        Args:
            data: 原始数据
            merge_mapping: 实体名称映射字典
            
        Returns:
            更新后的数据
        """
        updated_data = data.copy()
        
        # 更新实体属性中的实体名称
        if 'entity_attribute' in updated_data:
            for entity in updated_data['entity_attribute']:
                if 'name' in entity and entity['name'] in merge_mapping:
                    entity['name'] = merge_mapping[entity['name']]
        
        # 更新实体关系中的实体名称
        if 'entity_relation' in updated_data:
            for relation in updated_data['entity_relation']:
                if 'source_entity' in relation and relation['source_entity'] in merge_mapping:
                    relation['source_entity'] = merge_mapping[relation['source_entity']]
                
                if 'target_entity' in relation and relation['target_entity'] in merge_mapping:
                    relation['target_entity'] = merge_mapping[relation['target_entity']]
        
        return updated_data
    
    def merge_duplicate_entities(self, data: Dict) -> Dict:
        """
        合并重复的实体
        
        Args:
            data: 原始数据
            
        Returns:
            合并后的数据
        """
        updated_data = data.copy()
        
        # 合并实体属性中的重复实体
        if 'entity_attribute' in updated_data:
            entity_dict = {}
            
            for entity in updated_data['entity_attribute']:
                entity_name = entity['name']
                
                if entity_name in entity_dict:
                    # 合并属性
                    existing_entity = entity_dict[entity_name]
                    if 'attribute' in entity and 'attribute' in existing_entity:
                        existing_entity['attribute'].extend(entity['attribute'])
                else:
                    entity_dict[entity_name] = entity
            
            updated_data['entity_attribute'] = list(entity_dict.values())
        
        return updated_data
    
    def update_jsonl_file(self, input_file: str, output_file: str, 
                         merge_mapping: Dict[str, str]) -> int:
        """
        更新JSONL文件中的实体名称
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            merge_mapping: 实体名称映射字典
            
        Returns:
            更新的记录数
        """
        updated_count = 0
        
        print(f"正在更新文件: {input_file} -> {output_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f_in, \
             open(output_file, 'w', encoding='utf-8') as f_out:
            
            for line in tqdm(f_in, desc="更新实体名称"):
                try:
                    data = json.loads(line.strip())
                    
                    # 更新实体名称
                    updated_data = self.update_entity_names(data, merge_mapping)
                    
                    # 合并重复实体
                    updated_data = self.merge_duplicate_entities(updated_data)
                    
                    # 添加更新时间戳
                    updated_data['merged_time'] = datetime.now().isoformat()
                    
                    # 写入更新后的数据
                    f_out.write(json.dumps(updated_data, ensure_ascii=False) + '\n')
                    updated_count += 1
                    
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    continue
        
        print(f"成功更新 {updated_count} 条记录")
        return updated_count
    
    def save_merge_mapping(self, merge_mapping: Dict[str, str], output_file: str):
        """
        保存实体合并映射到文件
        
        Args:
            merge_mapping: 实体合并映射字典
            output_file: 输出文件路径
        """
        mapping_data = {
            "merge_mapping": merge_mapping,
            "merge_time": datetime.now().isoformat(),
            "total_mappings": len(merge_mapping)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, ensure_ascii=False, indent=2)
        
        print(f"合并映射已保存到: {output_file}")
    
    def load_merge_mapping(self, input_file: str) -> Dict[str, str]:
        """
        从文件加载实体合并映射
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            实体合并映射字典
        """
        with open(input_file, 'r', encoding='utf-8') as f:
            mapping_data = json.load(f)
        
        merge_mapping = mapping_data.get("merge_mapping", {})
        print(f"从 {input_file} 加载了 {len(merge_mapping)} 个合并映射")
        return merge_mapping
    
    def get_merge_statistics(self, merge_mapping: Dict[str, str]) -> Dict:
        """
        获取合并统计信息
        
        Args:
            merge_mapping: 实体合并映射字典
            
        Returns:
            统计信息字典
        """
        # 统计合并目标
        target_counts = {}
        for old_name, new_name in merge_mapping.items():
            if new_name not in target_counts:
                target_counts[new_name] = 0
            target_counts[new_name] += 1
        
        # 计算统计信息
        total_mappings = len(merge_mapping)
        unique_targets = len(target_counts)
        max_merge_count = max(target_counts.values()) if target_counts else 0
        
        statistics = {
            "total_mappings": total_mappings,
            "unique_targets": unique_targets,
            "max_merge_count": max_merge_count,
            "target_distribution": target_counts
        }
        
        return statistics
    
    def validate_merge_mapping(self, merge_mapping: Dict[str, str], 
                             entity_types: Dict[str, str]) -> Dict[str, List[str]]:
        """
        验证合并映射的有效性（已移除类型限制）
        
        Args:
            merge_mapping: 实体合并映射字典
            entity_types: 实体类型字典
            
        Returns:
            验证结果字典
        """
        validation_results = {
            "valid_mappings": [],
            "invalid_mappings": []
        }
        
        for old_name, new_name in merge_mapping.items():
            # 检查实体是否存在
            if old_name not in entity_types:
                validation_results["invalid_mappings"].append({
                    "old_name": old_name,
                    "new_name": new_name,
                    "reason": "原实体不存在"
                })
                continue
            
            if new_name not in entity_types:
                validation_results["invalid_mappings"].append({
                    "old_name": old_name,
                    "new_name": new_name,
                    "reason": "目标实体不存在"
                })
                continue
            
            # 所有存在的实体映射都认为是有效的（不检查类型匹配）
            old_type = entity_types[old_name]
            validation_results["valid_mappings"].append({
                "old_name": old_name,
                "new_name": new_name,
                "type": old_type
            })
        
        return validation_results
    
    def create_backup(self, file_path: str) -> str:
        """
        创建文件备份
        
        Args:
            file_path: 原文件路径
            
        Returns:
            备份文件路径
        """
        backup_path = file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if os.path.exists(file_path):
            import shutil
            shutil.copy2(file_path, backup_path)
            print(f"已创建备份文件: {backup_path}")
        
        return backup_path 