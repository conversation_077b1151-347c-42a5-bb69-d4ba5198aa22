# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 进度监控模块，支持断点继续和进度保存
"""

import json
import os
import pickle
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class ProgressMonitor:
    """进度监控器"""
    
    def __init__(self, progress_dir: str, model_name: str):
        """
        初始化进度监控器
        
        Args:
            progress_dir: 进度文件保存目录
            model_name: 模型名称
        """
        self.progress_dir = progress_dir
        self.model_name = model_name
        self.progress_file = os.path.join(progress_dir, f"{model_name}_merge_progress.pkl")
        self.stats_file = os.path.join(progress_dir, f"{model_name}_merge_stats.json")
        
        # 确保进度目录存在
        os.makedirs(progress_dir, exist_ok=True)
        
        # 初始化进度状态
        self.progress_state = {
            'start_time': None,
            'current_iteration': 0,
            'total_iterations': 0,
            'processed_entities': set(),
            'merged_pairs': [],
            'current_entity_pairs': [],
            'similarity_matrix': {},
            'entity_embeddings': {},
            'merge_decisions': {},
            'status': 'not_started'  # not_started, running, completed, failed
        }
        
        # 加载现有进度
        self.load_progress()
    
    def load_progress(self):
        """加载现有进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'rb') as f:
                    loaded_state = pickle.load(f)
                    
                # 更新进度状态
                for key, value in loaded_state.items():
                    if key in self.progress_state:
                        self.progress_state[key] = value
                
                logger.info(f"已加载进度文件: {self.progress_file}")
                logger.info(f"当前迭代轮数: {self.progress_state['current_iteration']}")
                logger.info(f"已处理实体数量: {len(self.progress_state['processed_entities'])}")
                logger.info(f"已合并实体对数量: {len(self.progress_state['merged_pairs'])}")
                
            except Exception as e:
                logger.warning(f"加载进度文件失败: {e}")
                logger.info("将从头开始执行")
    
    def save_progress(self):
        """保存当前进度"""
        try:
            # 更新保存时间
            self.progress_state['last_save_time'] = datetime.now().isoformat()
            
            # 保存进度文件
            with open(self.progress_file, 'wb') as f:
                pickle.dump(self.progress_state, f)
            
            # 保存统计信息
            stats = self.get_statistics()
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"进度已保存: {self.progress_file}")
            
        except Exception as e:
            logger.error(f"保存进度失败: {e}")
    
    def has_progress(self) -> bool:
        """检查是否有现有进度"""
        return os.path.exists(self.progress_file) and self.progress_state['status'] != 'not_started'
    
    def start_merge_process(self, total_iterations: int):
        """开始合并过程"""
        self.progress_state['start_time'] = datetime.now().isoformat()
        self.progress_state['total_iterations'] = total_iterations
        self.progress_state['status'] = 'running'
        self.save_progress()
        
        logger.info(f"开始合并过程，总迭代轮数: {total_iterations}")
    
    def update_iteration(self, iteration: int, entity_pairs: List[tuple]):
        """更新当前迭代"""
        self.progress_state['current_iteration'] = iteration
        self.progress_state['current_entity_pairs'] = entity_pairs
        self.save_progress()
        
        logger.info(f"更新到第 {iteration} 轮迭代，候选实体对数量: {len(entity_pairs)}")
    
    def add_merged_pair(self, entity1: str, entity2: str, merged_name: str, similarity: float, decision: str):
        """添加已合并的实体对"""
        merge_info = {
            'entity1': entity1,
            'entity2': entity2,
            'merged_name': merged_name,
            'similarity': similarity,
            'decision': decision,
            'iteration': self.progress_state['current_iteration'],
            'timestamp': datetime.now().isoformat()
        }
        
        self.progress_state['merged_pairs'].append(merge_info)
        self.progress_state['processed_entities'].add(entity1)
        self.progress_state['processed_entities'].add(entity2)
        
        self.save_progress()
        
        logger.info(f"添加合并对: {entity1} + {entity2} -> {merged_name} (相似度: {similarity:.3f})")
    
    def add_merge_decision(self, entity1: str, entity2: str, decision: str, reason: str = ""):
        """添加合并决策"""
        decision_key = f"{entity1}|||{entity2}"
        self.progress_state['merge_decisions'][decision_key] = {
            'decision': decision,
            'reason': reason,
            'iteration': self.progress_state['current_iteration'],
            'timestamp': datetime.now().isoformat()
        }
    
    def update_embeddings(self, embeddings: Dict[str, Any]):
        """更新实体嵌入"""
        self.progress_state['entity_embeddings'].update(embeddings)
    
    def update_similarity_matrix(self, similarity_matrix: Dict[tuple, float]):
        """更新相似度矩阵"""
        self.progress_state['similarity_matrix'].update(similarity_matrix)
    
    def complete_merge_process(self):
        """完成合并过程"""
        self.progress_state['status'] = 'completed'
        self.progress_state['end_time'] = datetime.now().isoformat()
        self.save_progress()
        
        logger.info("合并过程已完成")
    
    def fail_merge_process(self, error_message: str):
        """标记合并过程失败"""
        self.progress_state['status'] = 'failed'
        self.progress_state['error_message'] = error_message
        self.progress_state['end_time'] = datetime.now().isoformat()
        self.save_progress()
        
        logger.error(f"合并过程失败: {error_message}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        start_time = self.progress_state.get('start_time')
        end_time = self.progress_state.get('end_time')
        
        stats = {
            'model_name': self.model_name,
            'status': self.progress_state['status'],
            'current_iteration': self.progress_state['current_iteration'],
            'total_iterations': self.progress_state['total_iterations'],
            'processed_entities_count': len(self.progress_state['processed_entities']),
            'merged_pairs_count': len(self.progress_state['merged_pairs']),
            'merge_decisions_count': len(self.progress_state['merge_decisions']),
            'start_time': start_time,
            'end_time': end_time,
            'last_save_time': self.progress_state.get('last_save_time')
        }
        
        # 计算处理时间
        if start_time:
            start_dt = datetime.fromisoformat(start_time)
            if end_time:
                end_dt = datetime.fromisoformat(end_time)
                stats['total_duration'] = (end_dt - start_dt).total_seconds()
            else:
                current_dt = datetime.now()
                stats['current_duration'] = (current_dt - start_dt).total_seconds()
        
        # 合并决策统计
        decision_stats = {}
        for decision_info in self.progress_state['merge_decisions'].values():
            decision = decision_info['decision']
            decision_stats[decision] = decision_stats.get(decision, 0) + 1
        
        stats['decision_statistics'] = decision_stats
        
        return stats
    
    def get_resume_info(self) -> Dict[str, Any]:
        """获取断点继续信息"""
        if not self.has_progress():
            return {}
        
        return {
            'current_iteration': self.progress_state['current_iteration'],
            'processed_entities': list(self.progress_state['processed_entities']),
            'merged_pairs': self.progress_state['merged_pairs'],
            'entity_embeddings': self.progress_state['entity_embeddings'],
            'similarity_matrix': self.progress_state['similarity_matrix'],
            'merge_decisions': self.progress_state['merge_decisions']
        }
    
    def clear_progress(self):
        """清除进度"""
        if os.path.exists(self.progress_file):
            os.remove(self.progress_file)
        if os.path.exists(self.stats_file):
            os.remove(self.stats_file)
        
        # 重置进度状态
        self.progress_state = {
            'start_time': None,
            'current_iteration': 0,
            'total_iterations': 0,
            'processed_entities': set(),
            'merged_pairs': [],
            'current_entity_pairs': [],
            'similarity_matrix': {},
            'entity_embeddings': {},
            'merge_decisions': {},
            'status': 'not_started'
        }
        
        logger.info("进度已清除")
    
    def print_progress(self):
        """打印当前进度"""
        stats = self.get_statistics()
        
        print("\n" + "=" * 50)
        print("实体合并进度")
        print("=" * 50)
        print(f"模型名称: {stats['model_name']}")
        print(f"状态: {stats['status']}")
        print(f"当前迭代: {stats['current_iteration']}/{stats['total_iterations']}")
        print(f"已处理实体: {stats['processed_entities_count']}")
        print(f"已合并实体对: {stats['merged_pairs_count']}")
        print(f"合并决策数: {stats['merge_decisions_count']}")
        
        if 'decision_statistics' in stats:
            print("\n决策统计:")
            for decision, count in stats['decision_statistics'].items():
                print(f"  {decision}: {count}")
        
        if 'current_duration' in stats:
            print(f"\n当前运行时间: {stats['current_duration']:.1f} 秒")
        elif 'total_duration' in stats:
            print(f"\n总运行时间: {stats['total_duration']:.1f} 秒")
        
        print("=" * 50) 