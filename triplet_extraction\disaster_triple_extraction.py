# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 灾害事件三元组抽取
"""

import json
import pandas as pd
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
import concurrent.futures
from tqdm import tqdm
import re
import os
import pickle
from datetime import datetime

# 配置
BASE_URL = "https://open.bigmodel.cn/api/paas/v4"
API_KEY = "fdd92b30af6c46efb5d18e0eb6540816.c69FP2JZnAAMRzia"
MODEL = "glm-4-flash"
MODEL_NAME = "glm-4-flash"

# 创建langchain LLM实例
llm = ChatOpenAI(
    base_url=BASE_URL,
    api_key=API_KEY,
    model=MODEL,
    temperature=0.1
)

def load_excel_data(file_path, id_column='ID', text_column='文本语料'):
    """
    加载 Excel 数据文件
    """
    try:
        df = pd.read_excel(file_path)
        
        # 检查必要的列是否存在
        if id_column not in df.columns:
            print(f"警告: 未找到ID列 '{id_column}'，使用索引作为ID")
            df[id_column] = df.index
        
        if text_column not in df.columns:
            print(f"错误: 未找到文本语料列 '{text_column}'")
            print(f"可用列: {list(df.columns)}")
            return []
        
        # 过滤空值
        df = df.dropna(subset=[text_column])
        
        data = []
        for _, row in df.iterrows():
            item = {
                'id': str(row[id_column]),
                'text': str(row[text_column]).strip()
            }
            data.append(item)
        
        print(f"成功加载 {len(data)} 条数据")
        return data
        
    except Exception as e:
        print(f"加载Excel文件时出错: {e}")
        return []

def get_disaster_extraction_prompt():
    """
    获取灾害事件三元组抽取的中文提示词
    """
    return """
你是一位专业的自然灾害领域专家和知识抽取专家。请从给定的文本中抽取灾害事件相关的实体、属性和关系，构建三元组知识图谱。

## 抽取任务说明：

你需要进行两种类型的抽取：
1. **实体-属性-属性值**：抽取实体的属性信息
2. **实体-关系-实体**：抽取实体之间的关系

**重要说明**：
- 属性名称和关系名称必须严格按照下面定义的列表
- 只有实体名称可以由你从文本中抽取或概括

## 属性名称定义（用于实体-属性-属性值抽取）：
| 实体类型 | 属性名称     | 描述 |
|----------|--------------|------|
| 灾害事件 | 灾害名称     | 如“2010年玉树7.1级地震”“2018年金沙江山体滑坡”等具体灾害的名称 |
| 灾害事件 | 灾害类型     | 地震、滑坡、崩塌、泥石流、冰湖溃决等具体的灾害种类 |
| 灾害事件 | 灾害规模     | 描述灾害影响范围和强度的信息，例如“受灾面积”、“灾害体体积”等 |
| 灾害事件 | 灾害等级     | 依据相关评定标准确定的灾害等级，例如“地震震级”、“台风等级”等 |
| 灾害事件 | 灾害趋势     | 描述灾害发展变化的趋势，例如滑坡灾害日益加剧 |
| 灾害事件 | 灾害特征     | 灾害事件本身固有的性质和表现形式，例如“突发性”、“季节性”、“群发性”、“周期性”等 |
| 灾害事件 | 灾害效应     | 描述灾害发生后对社会、经济、环境和人类等多方面产生的影响 |
| 灾害事件 | 地理位置     | 灾害事件发生所在的行政区划名称 |
| 灾害事件 | 地理坐标     | 灾害事件发生地点的经纬度坐标 |
| 灾害事件 | 分布特征     | 描述灾害在空间上的分布特征 |
| 灾害事件 | 发生时间     | 灾害事件发生的时间 |
| 灾害事件 | 开始时间     | 灾害事件开始的时间 |
| 灾害事件 | 结束时间     | 灾害事件结束的时间 |
| 灾害事件 | 受灾人数     | 因灾害事件受到影响的人员数量 |
| 灾害事件 | 死亡人数     | 在灾害事件中死亡的人员数量，包括直接和间接死亡 |
| 灾害事件 | 受伤人数     | 在灾害事件中受伤的人员数量 |
| 灾害事件 | 失踪人数     | 在灾害事件中失踪的人员数量 |
| 灾害事件 | 经济损失     | 灾害事件造成的经济方面的损失金额 |
| 行政区划/地理单元 | 受灾情况     | 描述行政区划/地理单元（承灾体）的受灾情况 |
| 行政区划/地理单元 | 人口         | 该行政区划/地理单元内的人口数量 |
| 行政区划/地理单元 | 经济         | 经济总量、主要经济产业等经济相关信息 |
| 行政区划/地理单元 | 气候         | 主要的气候类型 |
| 行政区划/地理单元 | 降雨量       | 平均降雨量或灾害期间降雨量等数据 |
| 行政区划/地理单元 | 地形地貌     | 高原、山地等地形地貌特征 |
| 行政区划/地理单元 | 产业结构     | 产业构成及各产业占比等信息 |
| 行政区划/地理单元 | 主要灾害类型 | 主要发生的自然灾害类型 |
| 建筑物   | 受灾情况             | 描述建筑物（承灾体）的受灾情况 |
| 建筑物   | 建筑类型             | 住宅、商业建筑、工业建筑等 |
| 建筑物   | 建筑数量             | 在特定区域内的建筑物数量 |
| 建筑物   | 抗震设防烈度         | 建筑物能抵抗的地震烈度标准，如“抗震设防烈度8度” |
| 建筑物   | 洪水淹没风险等级     | 根据建筑物所处位置和防洪能力评估的风险等级 |
| 交通设施   | 受灾情况             | 描述交通设施（承灾体）的受灾情况 |
| 交通设施   | 设施类型             | 公路、铁路、机场等具体类型 |
| 交通设施   | 设施规模             | 公路长度、机场占地面积等 |
| 交通设施   | 运输能力             | 公路车流量、铁路货运量等 |
| 交通设施   | 洪水淹没风险等级     | 洪水淹没风险等级 |
| 交通设施   | 地震破坏风险等级     | 地震破坏风险等级 |
| 电力设施   | 受灾情况             | 描述电力设施（承灾体）的受灾情况 |
| 电力设施   | 设施类型             | 发电站、变电站、输电线路等类型 |
| 电力设施   | 供电范围             | 该电力设施负责供电的区域 |
| 电力设施   | 供电能力             | 发电量、供电功率等 |
| 电力设施   | 抗风能力等级         | 能抵御的风力等级标准 |
| 电力设施   | 地震破坏风险等级     | 地震破坏风险等级 |
| 水利设施   | 受灾情况             | 描述水利设施（承灾体）的受灾情况 |
| 水利设施   | 设施类型             | 水库、堤坝、灌溉渠道等类型 |
| 水利设施   | 设施功能             | 防洪、灌溉、供水等功能 |
| 水利设施   | 蓄水量               | 水库等水利设施的蓄水量 |
| 水利设施   | 地震破坏风险等级     | 地震破坏风险等级 |
| 水利设施   | 洪水漫溢风险等级     | 洪水漫溢风险等级 |
| 通讯设施   | 受灾情况             | 描述通讯设施（承灾体）的受灾情况 |
| 通讯设施   | 设施类型             | 通讯基站、网络线路等类型 |
| 通讯设施   | 覆盖范围             | 通讯信号覆盖的区域范围 |
| 通讯设施   | 通讯容量             | 基站可容纳的用户数量等 |
| 通讯设施   | 地震破坏风险等级     | 地震破坏风险等级 |
| 通讯设施   | 极端天气破坏风险等级 | 极端天气破坏风险等级 |
| 森林         | 受灾情况           | 描述森林（承灾体）的受灾情况 |
| 森林         | 面积               | 森林覆盖的面积大小 |
| 森林         | 植被类型           | 针叶林、阔叶林等植被类型 |
| 森林         | 优势物种           | 森林生态系统中占优势地位的物种名称 |
| 耕地/农田    | 受灾情况           | 描述耕地/农田（承灾体）的受灾情况 |
| 耕地/农田    | 面积               | 耕地/农田的面积 |
| 耕地/农田    | 农作物种植类型     | 例如“小麦”“水稻”等 |
| 河流         | 受灾情况           | 描述河流（承灾体）的受灾情况 |
| 河流         | 长度               | 河流的长度 |
| 河流         | 流量               | 河流的水流流量 |
| 河流         | 流域面积           | 河流流域覆盖的面积 |
| 草原         | 受灾情况           | 描述草原（承灾体）的受灾情况 |
| 草原         | 面积               | 草原的面积 |
| 草原         | 植被覆盖度         | 草原植被覆盖的比例 |
| 草原         | 载畜量             | 草原可承载的牲畜数量 |
| 湿地         | 受灾情况           | 描述湿地（承灾体）的受灾情况 |
| 湿地         | 面积               | 湿地的面积 |
| 湿地         | 生物多样性         | 湿地内生物种类及数量的丰富程度 |
| 地质构造   | 构造带名称     | 昆仑-秦岭构造带、祁连-龙门构造带等 |
| 地质构造   | 构造带类型     | 层内错动带、断裂、褶皱等 |
| 地质构造   | 构造带活动性   | 缩短、增厚、滑脱等运动特征及活跃程度 |
| 地质构造   | 构造带规模     | 构造带长度等 |
| 地形地貌   | 地形地貌特征   | 如青藏高原构造隆升强烈 |
| 地形地貌   | 地形地貌类型   | 高原、峡谷、冰川等类型 |
| 地形地貌   | 坡度           | 坡度数值范围，如“30 - 50度” |
| 地形地貌   | 高度           | 海拔高度范围，如“1500 - 2500m” |
| 地形地貌   | 冰川面积       | 冰川表面积 |
| 地形地貌   | 冻土类型       | 连续/不连续多年冻土/季节冻土 |
| 岩土体特征   | 地层岩性     | 千枚岩、砂泥岩、花岗岩等岩石类型 |
| 岩土体特征   | 岩层特性     | 硬度、透水性等 |
| 岩土体特征   | 土体类型     | 松散堆积物（如冰碛物、冲积扇）的抗侵蚀能力 |
| 岩土体特征   | 风化程度     | 温差风化速率，如“最大温差贡献率14.5%-15.1%” |
| 灾害应对措施   | 任务名称         | 各阶段应对任务的名称 |
| 灾害应对措施   | 任务描述         | 应对任务的具体内容描述 |
| 灾害应对措施   | 任务阶段         | 灾前、灾中、灾后 |
| 灾害应对措施   | 任务类别         | 风险评估、预警、救援、重建等 |
| 灾害应对措施   | 任务级别         | 对任务重要性或紧急程度的分级 |
| 灾害应对措施   | 任务发布时间     | 任务发布的时间 |
| 灾害应对措施   | 任务完成时间     | 任务完成的时间 |
| 灾害应对措施   | 任务完成效果     | 应对任务完成后的实际效果 |

## 关系名称定义（用于实体-关系-实体抽取）：
| 关系         | 描述 |
|--------------|------|
| 承灾体       | 承灾体有行政区划、建筑物、交通设施、电力设施、水利设施、通讯设施、森林、耕地、农田、河流、草原、湿地等，并且灾害事件会对其造成影响。如地震可能导致建筑物受损；洪水可能淹没农田；灾害还会对行政区划的人口、经济等属性产生影响。 |
| 致灾因子     | 表明引发灾害事件的原因，也就是致灾因子，包括地质因素（板块碰撞、断裂带活动、高地应力、陡峭地形等）、气象因素（强降雨、暴风雪、干旱、强对流天气等）、冰川因素（冰川退缩、冻土退化、融水洪水等）、人为因素（工程扰动、生态破坏、人类聚居区等）。 |
| 灾害应对措施 | 针对灾害事件所采取的措施，例如灾前的“防灾减灾措施”旨在预防灾害发生或减轻灾害影响；灾中的“应急响应措施”是在灾害发生时采取的紧急行动；灾后的“灾后恢复措施”则是对灾害造成的破坏进行修复和重建。 |
| 孕灾环境     | 表明灾害事件发生的特定区域及其特征，包括地质构造、地形地貌、岩土体特征。例如某次地震发生在特定的断裂带中，该环境的地形地貌、坡度高度、地层岩性等属性与灾害发生相关。 |
| 引发/导致    | 表明一个灾害事件引发/导致了另一个灾害事件。如地震灾害可能引发山体滑坡；冰湖溃决可能导致洪水的发生。 |
| 包括        | 用于描述一个灾害事件包含另一个较小规模或相关联的灾害事件。比如一场大规模的暴雨洪涝灾害可能包含了局部地区的城市内涝灾害；一次大型泥石流灾害可能包括了多个小规模的山体滑坡事件。 |
| 原生关系    | 标记最初发生、直接由致灾因子引起的灾害事件。如地震通常作为原生灾害，是由地壳运动等地质因素直接引发；火山喷发是由地下岩浆活动等地质因素导致的原生灾害。 |
| 次生关系     | 标记由原生灾害引发的后续灾害事件。比如地震发生后引发的火灾、海啸、山体滑坡等属于次生灾害；火山喷发后产生的火山灰导致的空气污染、农作物减产等后续灾害为次生灾害。 |
| 链生关系     | 描述一系列灾害事件之间的连锁反应关系，即一个灾


## 抽取示例：

**输入文本：**
2010年4月14日，青海省玉树州玉树县境内发生7.1级强烈地震，仪器震中位于33.2°N，96.6°E。主震发生后，截至2010年9月29日15时00分，共发生余震3262次，余震主要发生在玉树主震的南东侧。玉树地震的发震断裂为甘孜-玉树断裂带。该断裂带是青藏高原内部的一条大型左旋走滑活动断裂带，西起青海治多县那王草曲塘，向东南经当江、玉树邓柯、玉隆，至四川甘孜县城南，全长约500km。地震造成2698人遇难，270人失踪，总受灾人口达195710人。大量房屋倒塌，生命线系统严重受损，供水、供电、通讯一度中断，部分道路交通堵塞。玉树地震发生后，中国地震局于第一时间即组织了现场应急工作队，于地震当天即到达灾区现场，进行现场考察工作。

**输出格式：**
```json
{
    "entity_attribute": [
        {
            "name": "2010年青海省玉树州7.1级地震",
            "type": "灾害事件",
            "attribute": [
                {
                    "name": "发生时间",
                    "value": "2010年4月14日"
                },
                {
                    "name": "灾害类型",
                    "value": "地震"
                },
                {
                    "name": "灾害等级",
                    "value": "7.1级"
                },
                {
                    "name": "地理位置",
                    "value": "青海省玉树州玉树县"
                },
                {
                    "name": "地理坐标",
                    "value": "33.2°N，96.6°E"
                },
                {
                    "name": "遇难人数",
                    "value": "2698人"
                },
                {
                    "name": "失踪人数",
                    "value": "270人"
                },
                {
                    "name": "受灾人数",
                    "value": "195710人"
                }
            ]
        },
        {
            "name": "余震",
            "type": "灾害事件",
            "attribute": [
                {
                    "name": "结束时间",
                    "value": "2010年9月29日15时00分"
                },
                {
                    "name": "灾害规模",
                    "value": "3262次"
                },
                {
                    "name": "灾害类型",
                    "value": "地震"
                }
            ]
        },
        {
            "name": "甘孜-玉树断裂带",
            "type": "地质构造",
            "attribute": [
                {
                    "name": "构造带类型",
                    "value": "左旋走滑活动断裂带"
                },
                {
                    "name": "构造带规模",
                    "value": "全长约500km"
                }
            ]
        },
        {
            "name": "房屋",
            "type": "建筑物",
            "attribute": [
                {
                    "name": "受灾情况",
                    "value": "倒塌"
                }
            ]
        },
        {
            "name": "生命线系统",
            "type": "基础设施",
            "attribute": [
                {
                    "name": "受灾情况",
                    "value": "严重受损"
                }
            ]
        },
        {
            "name": "供水系统",
            "type": "基础设施",
            "attribute": [
                {
                    "name": "受灾情况",
                    "value": "中断"
                }
            ]
        },
        {
            "name": "供电系统",
            "type": "基础设施",
            "attribute": [
                {
                    "name": "受灾情况",
                    "value": "中断"
                }
            ]
        },
        {
            "name": "通讯系统",
            "type": "基础设施",
            "attribute": [
                {
                    "name": "受灾情况",
                    "value": "中断"
                }
            ]
        },
        {
            "name": "道路交通",
            "type": "交通设施",
            "attribute": [
                {
                    "name": "受灾情况",
                    "value": "堵塞"
                }
            ]
        },
        {
            "name": "现场考察工作",
            "type": "灾害应对措施",
            "attribute": [
                {
                    "name": "任务阶段",
                    "value": "灾后"
                },
                {
                    "name": "任务描述",
                    "value": "中国地震局第一时间组织现场应急工作队于地震当天到达灾区现场进行现场考察工作"
                },
                {
                    "name": "任务发布时间",
                    "value": "2010年4月14日"
                },
                {
                    "name": "任务类别",
                    "value": "现场考察"
                }
            ]
        }
    ],
    "entity_relation": [
        {
            "source_entity": "2010年青海省玉树州7.1级地震",
            "source_entity_type": "灾害事件",
            "target_entity": "甘孜-玉树断裂带",
            "target_entity_type": "地质构造",
            "relationship": "孕灾环境"
        },
        {
            "source_entity": "2010年青海省玉树州7.1级地震",
            "source_entity_type": "灾害事件",
            "target_entity": "房屋",
            "target_entity_type": "建筑物",
            "relationship": "承灾体"
        },
        {
            "source_entity": "2010年青海省玉树州7.1级地震",
            "source_entity_type": "灾害事件",
            "target_entity": "生命线系统",
            "target_entity_type": "基础设施",
            "relationship": "承灾体"
        },
        {
            "source_entity": "2010年青海省玉树州7.1级地震",
            "source_entity_type": "灾害事件",
            "target_entity": "供水系统",
            "target_entity_type": "基础设施",
            "relationship": "承灾体"
        },
        {
            "source_entity": "2010年青海省玉树州7.1级地震",
            "source_entity_type": "灾害事件",
            "target_entity": "供电系统",
            "target_entity_type": "基础设施",
            "relationship": "承灾体"
        },
        {
            "source_entity": "2010年青海省玉树州7.1级地震",
            "source_entity_type": "灾害事件",
            "target_entity": "通讯系统",
            "target_entity_type": "基础设施",
            "relationship": "承灾体"
        },
        {
            "source_entity": "2010年青海省玉树州7.1级地震",
            "source_entity_type": "灾害事件",
            "target_entity": "道路交通",
            "target_entity_type": "交通设施",
            "relationship": "承灾体"
        },
        {
            "source_entity": "2010年青海省玉树州7.1级地震",
            "source_entity_type": "灾害事件",
            "target_entity": "现场考察工作",
            "target_entity_type": "灾害应对措施",
            "relationship": "灾害应对措施"
        }
    ]
}
```

## 输出格式要求：

请严格按照以下JSON格式输出，包含实体属性和实体关系两个部分：

```json
{
    "entity_attribute": [
        {
            "name": "实体名称",
            "type": "实体类型",
            "attribute": [
                {
                    "name": "属性名称",
                    "value": "属性值"
                }
            ]
        }
    ],
    "entity_relation": [
        {
            "source_entity": "源实体名称",
            "source_entity_type": "源实体类型",
            "target_entity": "目标实体名称",
            "target_entity_type": "目标实体类型",
            "relationship": "关系名称"
        }
    ]
}
```

## 注意事项：

1. 实体名称可以使用原文的内容或者概括
2. 属性名称必须严格按照上面定义的属性名称列表
3. 关系名称必须严格按照上面定义的关系名称列表
4. 只抽取明确存在的关系，避免推测

## 输入文本：

{text}

请开始抽取：
"""

def chat(text):
    """
    与模型交互，用于抽取灾害事件三元组
    """
    prompt = get_disaster_extraction_prompt()
    user_prompt = prompt.replace("{text}", text)
   
    
    try:
        
        response = llm.invoke(
           [
                SystemMessage(content="你是一位专业的自然灾害领域专家和知识抽取专家，擅长从文本中准确识别和抽取灾害事件相关的实体、关系和属性。"),
                HumanMessage(content=user_prompt)
            ]
        )
        if response.content:
            return response.content
        else:
            print(f"模型响应错误: {response}")
            return None
    except Exception as e:
        print(f"调用模型时出错: {e}")
        return None



def load_progress(progress_file):
    """
    加载处理进度
    """
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            print(f"加载进度文件失败: {e}")
    return {'processed_ids': set(), 'failed_items': []}

def save_progress(progress_file, processed_ids, failed_items):
    """
    保存处理进度
    """
    try:
        with open(progress_file, 'wb') as f:
            pickle.dump({
                'processed_ids': processed_ids,
                'failed_items': failed_items
            }, f)
    except Exception as e:
        print(f"保存进度文件失败: {e}")

def process_item(item, progress_data):
    """
    处理单条数据项
    """
    item_id = item.get('id')
    text = item.get('text')
    
    # 检查是否已处理过
    if item_id in progress_data['processed_ids']:
        return None, None  # 跳过已处理的项目
    
    if text is None or text.strip() == "":
        print(f"警告: 数据项中缺少文本内容: {item}")
        return None, item
    
    result = chat(text)
    
    if result:
        try:
            result = result.replace("```json", "").replace("```", "")
            # 直接解析JSON对象格式
            try:
                parsed_result = json.loads(result)
                
                if isinstance(parsed_result, dict) and 'entity_attribute' in parsed_result and 'entity_relation' in parsed_result:
                    # 新格式
                    output_item = {
                        "id": item_id,
                        "text": text,
                        "entity_attribute": parsed_result.get('entity_attribute', []),
                        "entity_relation": parsed_result.get('entity_relation', []),
                        "processed_time": datetime.now().isoformat()
                    }
                    return output_item, None
                else:
                    print(f"响应格式不符合预期，原始响应: {result[:200]}...")
                    return None, item
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print(f"原始响应: {result[:200]}...")
                return None, item
            
        except Exception as e:
            print(f"处理文本时发生错误: {e}")
            print(f"原始响应: {result[:200]}...")
            return None, item
    
    return None, item

def save_batch_to_jsonl(results, filename, mode="a"):
    """
    批量保存结果到JSONL文件
    """
    with open(filename, mode, encoding="utf-8") as f:
        for data in results:
            if data:
                f.write(json.dumps(data, ensure_ascii=False) + "\n")



def main():
    # 文件路径配置
    input_file = "../data/data.xlsx"
    output_dir = f"../data/triplet_extraction/output/{MODEL_NAME}/"
    bad_output_dir = f"../data/triplet_extraction/bad/{MODEL_NAME}/"
    progress_file = f"../data/triplet_extraction/progress/{MODEL_NAME}_progress.pkl"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(bad_output_dir, exist_ok=True)
    os.makedirs("../data/triplet_extraction/progress/", exist_ok=True)
    
    # 输出文件路径
    output_jsonl = f"{output_dir}{MODEL_NAME}.jsonl"
    bad_output_file = f"{bad_output_dir}{MODEL_NAME}.jsonl"
    
    # 加载数据
    print("正在加载数据...")
    data = load_excel_data(input_file)
    if not data:
        print(f"无法加载数据文件: {input_file}")
        return
    
    # 加载处理进度
    print("正在加载处理进度...")
    progress_data = load_progress(progress_file)
    processed_count = len(progress_data['processed_ids'])
    failed_count = len(progress_data['failed_items'])
    
    print(f"已处理: {processed_count} 条")
    print(f"失败: {failed_count} 条")
    print(f"待处理: {len(data) - processed_count} 条")
    
    # 过滤已处理的数据
    data_to_process = [item for item in data if item['id'] not in progress_data['processed_ids']]
    
    if not data_to_process:
        print("所有数据已处理完成！")
        return
    
    # 处理配置
    batch_size = 10
    max_workers = 20  # 降低并发数以避免API限制
    results = []
    bad_data_results = []
    current_processed = 0
    
    # 清空输出文件（首次运行）
    if processed_count == 0:
        open(output_jsonl, 'w').close()
        open(bad_output_file, 'w').close()
    
    print(f"开始处理 {len(data_to_process)} 条数据...")
    
    # 使用线程池处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_item, item, progress_data) for item in data_to_process]
        
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures)):
            try:
                result, bad_data = future.result()
                
                if result:
                    results.append(result)
                    progress_data['processed_ids'].add(result['id'])
                elif bad_data:
                    bad_data_results.append(bad_data)
                
                current_processed += 1
                
                # 批量保存
                if current_processed % batch_size == 0:
                    save_batch_to_jsonl(results, output_jsonl)
                    save_batch_to_jsonl(bad_data_results, bad_output_file)
                    save_progress(progress_file, progress_data['processed_ids'], progress_data['failed_items'])
                    
                    print(f"\n已处理 {current_processed} 条数据，成功 {len(results)} 条，失败 {len(bad_data_results)} 条")
                    results = []
                    bad_data_results = []
                    
            except Exception as e:
                print(f"处理任务时发生错误: {e}")
    
    # 保存剩余结果
    if results:
        save_batch_to_jsonl(results, output_jsonl)
        print(f"\n最后保存成功数据 {len(results)} 条")
    
    if bad_data_results:
        save_batch_to_jsonl(bad_data_results, bad_output_file)
        print(f"\n最后保存失败数据 {len(bad_data_results)} 条")
    
    # 保存最终进度
    save_progress(progress_file, progress_data['processed_ids'], progress_data['failed_items'])
    
    print("处理完成！")
    print(f"结果文件:")
    print(f"  JSONL: {output_jsonl}")
    print(f"  失败数据: {bad_output_file}")

if __name__ == "__main__":
    main() 