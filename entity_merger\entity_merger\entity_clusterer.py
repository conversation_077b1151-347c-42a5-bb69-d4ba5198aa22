# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 实体聚类模块，基于相似度进行实体分组
"""

import numpy as np
from typing import Dict, List, Tuple, Set
from collections import defaultdict
import json
from tqdm import tqdm

class EntityClusterer:
    """实体聚类器"""
    
    def __init__(self, similarity_threshold: float = 0.8):
        """
        初始化聚类器
        
        Args:
            similarity_threshold: 相似度阈值
        """
        self.similarity_threshold = similarity_threshold
    
    def compute_similarity_matrix(self, embeddings: Dict[str, np.ndarray]) -> Dict[Tuple[str, str], float]:
        """
        计算实体间的相似度矩阵
        
        Args:
            embeddings: 实体嵌入字典
            
        Returns:
            相似度矩阵字典
        """
        similarity_matrix = {}
        entity_names = list(embeddings.keys())
        
        print("正在计算相似度矩阵...")
        for i in tqdm(range(len(entity_names)), desc="计算相似度"):
            for j in range(i + 1, len(entity_names)):
                entity1 = entity_names[i]
                entity2 = entity_names[j]
                
                # 计算余弦相似度
                embedding1 = embeddings[entity1]
                embedding2 = embeddings[entity2]
                
                dot_product = np.dot(embedding1, embedding2)
                norm1 = np.linalg.norm(embedding1)
                norm2 = np.linalg.norm(embedding2)
                
                if norm1 == 0 or norm2 == 0:
                    similarity = 0.0
                else:
                    similarity = dot_product / (norm1 * norm2)
                
                similarity_matrix[(entity1, entity2)] = float(similarity)
                similarity_matrix[(entity2, entity1)] = float(similarity)
        
        return similarity_matrix
    
    def find_similar_groups(self, embeddings: Dict[str, np.ndarray]) -> List[List[str]]:
        """
        基于相似度阈值找到相似的实体组
        
        Args:
            embeddings: 实体嵌入字典
            
        Returns:
            相似实体组列表
        """
        similarity_matrix = self.compute_similarity_matrix(embeddings)
        
        # 使用并查集进行聚类
        parent = {}
        
        def find(x):
            if x not in parent:
                parent[x] = x
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]
        
        def union(x, y):
            px, py = find(x), find(y)
            if px != py:
                parent[px] = py
        
        # 根据相似度阈值合并实体
        entity_names = list(embeddings.keys())
        for i in range(len(entity_names)):
            for j in range(i + 1, len(entity_names)):
                entity1 = entity_names[i]
                entity2 = entity_names[j]
                
                similarity = similarity_matrix.get((entity1, entity2), 0.0)
                if similarity >= self.similarity_threshold:
                    union(entity1, entity2)
        
        # 收集聚类结果
        clusters = defaultdict(list)
        for entity in entity_names:
            root = find(entity)
            clusters[root].append(entity)
        
        # 过滤掉只有一个实体的聚类
        valid_clusters = [cluster for cluster in clusters.values() if len(cluster) > 1]
        
        print(f"找到 {len(valid_clusters)} 个相似实体组")
        return valid_clusters
    
    def find_similar_pairs(self, embeddings: Dict[str, np.ndarray]) -> List[Tuple[str, str, float]]:
        """
        找到相似度超过阈值的实体对
        
        Args:
            embeddings: 实体嵌入字典
            
        Returns:
            相似实体对列表，每个元素为 (实体1, 实体2, 相似度)
        """
        similarity_matrix = self.compute_similarity_matrix(embeddings)
        
        similar_pairs = []
        for (entity1, entity2), similarity in similarity_matrix.items():
            if entity1 < entity2 and similarity >= self.similarity_threshold:  # 避免重复
                similar_pairs.append((entity1, entity2, similarity))
        
        # 按相似度降序排序
        similar_pairs.sort(key=lambda x: x[2], reverse=True)
        
        print(f"找到 {len(similar_pairs)} 对相似实体")
        return similar_pairs
    
    def cluster_by_type(self, embeddings: Dict[str, np.ndarray], 
                       entity_types: Dict[str, str]) -> Dict[str, List[List[str]]]:
        """
        按实体类型进行聚类
        
        Args:
            embeddings: 实体嵌入字典
            entity_types: 实体类型字典
            
        Returns:
            按类型分组的聚类结果
        """
        # 按类型分组实体
        type_groups = defaultdict(list)
        for entity_name in embeddings.keys():
            entity_type = entity_types.get(entity_name, "未知")
            type_groups[entity_type].append(entity_name)
        
        # 对每个类型进行聚类
        type_clusters = {}
        for entity_type, entities in type_groups.items():
            if len(entities) < 2:
                continue
                
            # 提取该类型实体的嵌入
            type_embeddings = {entity: embeddings[entity] for entity in entities}
            
            # 进行聚类
            clusters = self.find_similar_groups(type_embeddings)
            if clusters:
                type_clusters[entity_type] = clusters
        
        return type_clusters
    
    def get_merge_candidates(self, embeddings: Dict[str, np.ndarray], 
                           max_candidates: int = 50) -> List[Tuple[str, str, float]]:
        """
        获取合并候选对，按相似度排序
        
        Args:
            embeddings: 实体嵌入字典
            max_candidates: 最大候选对数
            
        Returns:
            合并候选对列表
        """
        similar_pairs = self.find_similar_pairs(embeddings)
        
        # 限制候选对数量
        candidates = similar_pairs[:max_candidates]
        
        print(f"选择前 {len(candidates)} 对作为合并候选")
        return candidates
    
    def save_clusters(self, clusters: List[List[str]], output_file: str):
        """
        保存聚类结果到文件
        
        Args:
            clusters: 聚类结果
            output_file: 输出文件路径
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(clusters, f, ensure_ascii=False, indent=2)
        
        print(f"聚类结果已保存到: {output_file}")
    
    def load_clusters(self, input_file: str) -> List[List[str]]:
        """
        从文件加载聚类结果
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            聚类结果
        """
        with open(input_file, 'r', encoding='utf-8') as f:
            clusters = json.load(f)
        
        print(f"从 {input_file} 加载了 {len(clusters)} 个聚类")
        return clusters 