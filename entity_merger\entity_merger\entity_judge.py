# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: LLM判断模块，使用大模型判断实体是否应该合并
"""

import json
from typing import List, Tuple, Dict, Optional
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from tqdm import tqdm
import time

class EntityJudge:
    """实体合并判断器"""
    
    def __init__(self, base_url: str, api_key: str, model: str = "glm-4-flash"):
        """
        初始化判断器
        
        Args:
            base_url: API基础URL
            api_key: API密钥
            model: 模型名称
        """
        self.llm = ChatOpenAI(
            base_url=base_url,
            api_key=api_key,
            model=model,
            temperature=0.1
        )
        self.model_name = model
    
    def get_merge_judgment_prompt(self, entity_pairs: List[Tuple[str, str, float]]) -> str:
        """
        生成实体合并判断的提示词
        
        Args:
            entity_pairs: 实体对列表，每个元素为 (实体1, 实体2, 相似度)
            
        Returns:
            提示词字符串
        """
        prompt = """
你是一位专业的自然灾害领域专家，负责判断两个实体是否应该合并。

## 任务说明：
你需要判断给定的实体对是否应该合并为同一个实体。合并的标准是：
1. 两个实体在语义上表示同一个概念或对象
2. 合并后不会造成信息丢失或语义混淆
3. 合并后的名称能够准确代表原实体的含义

## 判断原则：
- 如果两个实体表示同一个地理区域、灾害类型、设施等，应该合并
- 如果两个实体有包含关系（如"青藏高原"和"青藏高原东缘"），应该合并
- 如果两个实体是同一事物的不同表述（如"冰崩"和"冰川崩塌"），应该合并
- 如果两个实体虽然相似但表示不同对象，不应该合并
- 如果不确定，倾向于不合并

## 输出格式：
请严格按照以下JSON格式返回结果，不要添加任何其他内容：

```json
{
  "merge_decisions": [
    {
      "entity1": "实体1名称",
      "entity2": "实体2名称", 
      "similarity": 相似度值,
      "should_merge": true/false,
      "merged_name": "合并后的名称（如果合并）",
      "reason": "合并或不合并的理由"
    }
  ]
}
```

## 需要判断的实体对：
"""
        
        for i, (entity1, entity2, similarity) in enumerate(entity_pairs, 1):
            prompt += f"{i}. 实体1: {entity1}, 实体2: {entity2}, 相似度: {similarity:.3f}\n"
        
        prompt += "\n请开始判断："
        return prompt
    
    def judge_merge_candidates(self, entity_pairs: List[Tuple[str, str, float]], 
                             batch_size: int = 10) -> List[Dict]:
        """
        判断合并候选对
        
        Args:
            entity_pairs: 实体对列表
            batch_size: 批处理大小
            
        Returns:
            判断结果列表
        """
        all_decisions = []
        
        # 分批处理
        for i in range(0, len(entity_pairs), batch_size):
            batch = entity_pairs[i:i + batch_size]
            print(f"正在处理第 {i//batch_size + 1} 批，共 {len(batch)} 对实体...")
            
            try:
                # 生成提示词
                prompt = self.get_merge_judgment_prompt(batch)
                
                # 调用LLM
                response = self.llm.invoke([
                    SystemMessage(content="你是一位专业的自然灾害领域专家，负责判断实体是否应该合并。请严格按照JSON格式返回结果。"),
                    HumanMessage(content=prompt)
                ])
                
                # 解析响应
                if response.content:
                    try:
                        # 提取JSON部分
                        content = response.content.strip()
                        json_content = content.replace("```json", "").replace("```", "")
                        
                        result = json.loads(json_content)
                        
                        if "merge_decisions" in result:
                            all_decisions.extend(result["merge_decisions"])
                        else:
                            # 如果没有merge_decisions字段，尝试直接解析
                            all_decisions.extend(result)
                            
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {e}")
                        print(f"响应内容: {response.content}")
                        # 如果解析失败，为每个实体对创建默认决策
                        for entity1, entity2, similarity in batch:
                            all_decisions.append({
                                "entity1": entity1,
                                "entity2": entity2,
                                "similarity": similarity,
                                "should_merge": False,
                                "merged_name": "",
                                "reason": "LLM响应解析失败，默认不合并"
                            })
                
                # 添加延迟避免API限制
                time.sleep(1)
                
            except Exception as e:
                print(f"处理批次时出错: {e}")
                # 为当前批次创建默认决策
                for entity1, entity2, similarity in batch:
                    all_decisions.append({
                        "entity1": entity1,
                        "entity2": entity2,
                        "similarity": similarity,
                        "should_merge": False,
                        "merged_name": "",
                        "reason": f"处理出错: {str(e)}"
                    })
        
        return all_decisions
    
    def filter_merge_decisions(self, decisions: List[Dict]) -> List[Dict]:
        """
        过滤出应该合并的决策
        
        Args:
            decisions: 所有决策列表
            
        Returns:
            应该合并的决策列表
        """
        merge_decisions = [d for d in decisions if d.get("should_merge", False)]
        print(f"从 {len(decisions)} 个决策中筛选出 {len(merge_decisions)} 个合并决策")
        return merge_decisions
    
    def get_merge_mapping(self, merge_decisions: List[Dict]) -> Dict[str, str]:
        """
        从合并决策中生成实体映射关系
        
        Args:
            merge_decisions: 合并决策列表
            
        Returns:
            实体名称映射字典 {原名称: 新名称}
        """
        merge_mapping = {}
        
        for decision in merge_decisions:
            entity1 = decision["entity1"]
            entity2 = decision["entity2"]
            merged_name = decision.get("merged_name", "")
            
            if merged_name:
                merge_mapping[entity1] = merged_name
                merge_mapping[entity2] = merged_name
            else:
                # 如果没有提供合并名称，使用第一个实体名称
                merge_mapping[entity2] = entity1
        
        return merge_mapping
    
    def save_merge_decisions(self, decisions: List[Dict], output_file: str):
        """
        保存合并决策到文件
        
        Args:
            decisions: 合并决策列表
            output_file: 输出文件路径
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(decisions, f, ensure_ascii=False, indent=2)
        
        print(f"合并决策已保存到: {output_file}")
    
    def load_merge_decisions(self, input_file: str) -> List[Dict]:
        """
        从文件加载合并决策
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            合并决策列表
        """
        with open(input_file, 'r', encoding='utf-8') as f:
            decisions = json.load(f)
        
        print(f"从 {input_file} 加载了 {len(decisions)} 个合并决策")
        return decisions
    
    def validate_merge_decisions(self, decisions: List[Dict]) -> List[Dict]:
        """
        验证合并决策的有效性
        
        Args:
            decisions: 合并决策列表
            
        Returns:
            验证后的决策列表
        """
        valid_decisions = []
        
        for decision in decisions:
            # 检查必要字段
            required_fields = ["entity1", "entity2", "should_merge"]
            if not all(field in decision for field in required_fields):
                print(f"跳过无效决策: 缺少必要字段")
                continue
            
            # 检查合并名称
            if decision.get("should_merge", False):
                merged_name = decision.get("merged_name", "")
                if not merged_name:
                    # 如果没有合并名称，使用第一个实体名称
                    decision["merged_name"] = decision["entity1"]
                    decision["reason"] = decision.get("reason", "") + " (使用第一个实体名称作为合并名称)"
            
            valid_decisions.append(decision)
        
        print(f"验证后保留 {len(valid_decisions)} 个有效决策")
        return valid_decisions 