# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 属性名和关系名标准化
"""

import json
import pandas as pd
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from tqdm import tqdm
import os
from datetime import datetime
from collections import defaultdict

# 配置
BASE_URL = "https://open.bigmodel.cn/api/paas/v4"
API_KEY = "fdd92b30af6c46efb5d18e0eb6540816.c69FP2JZnAAMRzia"
MODEL = "glm-4-flash"
MODEL_NAME = "glm-4-flash"

# 创建langchain LLM实例
llm = ChatOpenAI(
    base_url=BASE_URL,
    api_key=API_KEY,
    model=MODEL,
    temperature=0.1
)

# 预定义的标准化属性名和关系名
STANDARD_ATTRIBUTES = {
    # 灾害事件属性
    "灾害名称", "灾害类型", "灾害规模", "灾害等级", "灾害趋势", "灾害特征", "灾害效应",
    "地理位置", "地理坐标", "分布特征", "发生时间", "开始时间", "结束时间",
    "受灾人数", "死亡人数", "受伤人数", "失踪人数", "经济损失",
    
    # 行政区划/地理单元属性
    "受灾情况", "人口", "经济", "气候", "降雨量", "地形地貌", "产业结构", "主要灾害类型",
    
    # 建筑物属性
    "建筑类型", "建筑数量", "抗震设防烈度", "洪水淹没风险等级",
    
    # 交通设施属性
    "设施类型", "设施规模", "运输能力", "地震破坏风险等级",
    
    # 电力设施属性
    "设施功能", "供电范围", "供电能力", "抗风能力等级",
    
    # 水利设施属性
    "设施功能", "蓄水量", "洪水漫溢风险等级",
    
    # 通讯设施属性
    "覆盖范围", "通讯容量", "极端天气破坏风险等级",
    
    # 森林属性
    "面积", "植被类型", "优势物种",
    
    # 耕地/农田属性
    "面积", "农作物种植类型",
    
    # 河流属性
    "长度", "流量", "流域面积",
    
    # 草原属性
    "面积", "植被覆盖度", "载畜量",
    
    # 湿地属性
    "面积", "生物多样性",
    
    # 地质构造属性
    "构造带名称", "构造带类型", "构造带活动性", "构造带规模",
    
    # 地形地貌属性
    "地形地貌特征", "地形地貌类型", "坡度", "高度", "冰川面积", "冻土类型",
    
    # 岩土体特征属性
    "地层岩性", "岩层特性", "土体类型", "风化程度",
    
    # 灾害应对措施属性
    "任务名称", "任务描述", "任务阶段", "任务类别", "任务级别", "任务发布时间", "任务完成时间", "任务完成效果"
}

STANDARD_RELATIONS = {
    "承灾体", "致灾因子", "灾害应对措施", "孕灾环境", "引发/导致", "包括", "原生关系", "次生关系", "链生关系"
}

def get_attribute_standardization_prompt():
    """
    获取属性名标准化的提示词
    """
    return """
你是一位专业的自然灾害领域专家，负责将非标准化的属性名规范化为标准名称。

## 标准化任务说明：
你需要将给定的属性名规范化为最接近的标准化属性名称。如果找不到合适的标准化名称，请保持原名。

## 标准化属性名称列表：

### 灾害事件属性：
灾害名称、灾害类型、灾害规模、灾害等级、灾害趋势、灾害特征、灾害效应、地理位置、地理坐标、分布特征、发生时间、开始时间、结束时间、受灾人数、死亡人数、受伤人数、失踪人数、经济损失

### 行政区划/地理单元属性：
受灾情况、人口、经济、气候、降雨量、地形地貌、产业结构、主要灾害类型

### 建筑物属性：
建筑类型、建筑数量、抗震设防烈度、洪水淹没风险等级

### 交通设施属性：
设施类型、设施规模、运输能力、地震破坏风险等级

### 电力设施属性：
设施功能、供电范围、供电能力、抗风能力等级

### 水利设施属性：
设施功能、蓄水量、洪水漫溢风险等级

### 通讯设施属性：
覆盖范围、通讯容量、极端天气破坏风险等级

### 森林属性：
面积、植被类型、优势物种

### 耕地/农田属性：
面积、农作物种植类型

### 河流属性：
长度、流量、流域面积

### 草原属性：
面积、植被覆盖度、载畜量

### 湿地属性：
面积、生物多样性

### 地质构造属性：
构造带名称、构造带类型、构造带活动性、构造带规模

### 地形地貌属性：
地形地貌特征、地形地貌类型、坡度、高度、冰川面积、冻土类型

### 岩土体特征属性：
地层岩性、岩层特性、土体类型、风化程度

### 灾害应对措施属性：
任务名称、任务描述、任务阶段、任务类别、任务级别、任务发布时间、任务完成时间、任务完成效果



## 输出格式：
请严格按照以下JSON格式返回结果，不要添加任何其他内容：

```json
{
  "standardized_names": {
    "原始属性名1": "标准化属性名1",
    "原始属性名2": "标准化属性名2",
    "原始属性名3": "标准化属性名3"
  }
}
```

## 需要标准化的属性名称：
{names}

请开始标准化：
"""

def get_relation_standardization_prompt():
    """
    获取关系名标准化的提示词
    """
    return """
你是一位专业的自然灾害领域专家，负责将非标准化的关系名规范化为标准名称。

## 标准化任务说明：
你需要将给定的关系名规范化为最接近的标准化关系名称。如果找不到合适的标准化名称，请保持原名。

## 标准化关系名称列表：
承灾体、致灾因子、灾害应对措施、孕灾环境、引发/导致、包括、原生关系、次生关系、链生关系

## 输出格式：
请严格按照以下JSON格式返回结果，不要添加任何其他内容：

```json
{
  "standardized_names": {
    "原始关系名1": "标准化关系名1",
    "原始关系名2": "标准化关系名2",
    "原始关系名3": "标准化关系名3"
  }
}
```

## 需要标准化的关系名称：
{names}

请开始标准化：
"""

def extract_names_from_jsonl(file_path):
    """
    从JSONL文件中提取所有属性名和关系名
    """
    attribute_names = set()
    relation_names = set()
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line.strip())
                
                # 提取属性名
                if 'entity_attribute' in data:
                    for entity in data['entity_attribute']:
                        if 'attribute' in entity:
                            for attr in entity['attribute']:
                                if 'name' in attr:
                                    attribute_names.add(attr['name'])
                
                # 提取关系名
                if 'entity_relation' in data:
                    for relation in data['entity_relation']:
                        if 'relationship' in relation:
                            relation_names.add(relation['relationship'])
                            
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                continue
    
    return attribute_names, relation_names

def filter_non_standard_names(attribute_names, relation_names):
    """
    过滤出非标准化的名称
    """
    non_standard_attributes = attribute_names - STANDARD_ATTRIBUTES
    non_standard_relations = relation_names - STANDARD_RELATIONS
    
    return non_standard_attributes, non_standard_relations

def standardize_attributes_batch(names, batch_size=10):
    """
    批量标准化属性名
    """
    standardized_names = {}
    
    # 将名称分批处理
    name_batches = [list(names)[i:i+batch_size] for i in range(0, len(names), batch_size)]
    
    for batch in tqdm(name_batches, desc="标准化属性名"):
        try:
            prompt = get_attribute_standardization_prompt().replace("{names}", "\n".join(batch))
            
            response = llm.invoke([
                SystemMessage(content="你是一位专业的自然灾害领域专家，负责将非标准化的属性名规范化为标准名称。请严格按照JSON格式返回结果。"),
                HumanMessage(content=prompt)
            ])

            
            if response.content:
                # 尝试解析JSON响应
                try:
                    # 提取JSON部分
                    content = response.content.strip()
                    json_content = content.replace("```json", "").replace("```", "")
                    
                    result = json.loads(json_content)
                    
                    if "standardized_names" in result:
                        standardized_names.update(result["standardized_names"])
                    else:
                        # 如果没有standardized_names字段，尝试直接解析
                        standardized_names.update(result)
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    print(f"响应内容: {response.content}")
                    # 如果JSON解析失败，保持原名
                    for name in batch:
                        standardized_names[name] = name
                            
        except Exception as e:
            print(f"批量标准化属性名出错: {e}")
            # 如果出错，保持原名
            for name in batch:
                standardized_names[name] = name
    
    return standardized_names

def standardize_relations_batch(names, batch_size=10):
    """
    批量标准化关系名
    """
    standardized_names = {}
    
    # 将名称分批处理
    name_batches = [list(names)[i:i+batch_size] for i in range(0, len(names), batch_size)]
    
    for batch in tqdm(name_batches, desc="标准化关系名"):
        try:
            prompt = get_relation_standardization_prompt().replace("{names}", "\n".join(batch))
            
            response = llm.invoke([
                SystemMessage(content="你是一位专业的自然灾害领域专家，负责将非标准化的关系名规范化为标准名称。请严格按照JSON格式返回结果。"),
                HumanMessage(content=prompt)
            ])
            
            if response.content:
                # 尝试解析JSON响应
                try:
                    # 提取JSON部分
                    content = response.content.strip()
                    json_content = content.replace("```json", "").replace("```", "")
                    
                    result = json.loads(json_content)
                    
                    if "standardized_names" in result:
                        standardized_names.update(result["standardized_names"])
                    else:
                        # 如果没有standardized_names字段，尝试直接解析
                        standardized_names.update(result)
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    print(f"响应内容: {response.content}")
                    # 如果JSON解析失败，保持原名
                    for name in batch:
                        standardized_names[name] = name
                            
        except Exception as e:
            print(f"批量标准化关系名出错: {e}")
            # 如果出错，保持原名
            for name in batch:
                standardized_names[name] = name
    
    return standardized_names

def replace_names_in_jsonl(input_file, output_file, attribute_mapping, relation_mapping):
    """
    在JSONL文件中替换名称
    """
    with open(input_file, 'r', encoding='utf-8') as f_in, \
         open(output_file, 'w', encoding='utf-8') as f_out:
        
        for line in f_in:
            try:
                data = json.loads(line.strip())
                
                # 替换属性名
                if 'entity_attribute' in data:
                    for entity in data['entity_attribute']:
                        if 'attribute' in entity:
                            for attr in entity['attribute']:
                                if 'name' in attr and attr['name'] in attribute_mapping:
                                    attr['name'] = attribute_mapping[attr['name']]
                
                # 替换关系名
                if 'entity_relation' in data:
                    for relation in data['entity_relation']:
                        if 'relationship' in relation and relation['relationship'] in relation_mapping:
                            relation['relationship'] = relation_mapping[relation['relationship']]
                
                # 添加标准化时间戳
                data['standardized_time'] = datetime.now().isoformat()
                
                f_out.write(json.dumps(data, ensure_ascii=False) + '\n')
                
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                continue

def main():
    """
    主函数
    """
    # 文件路径配置
    input_file = f"../data/triplet_extraction/output/glm-4-flash/glm-4-flash.jsonl"
    output_dir = f"../data/standardized_att_re/{MODEL_NAME}/"
    output_file = f"{output_dir}{MODEL_NAME}_standardized.jsonl"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print("开始标准化处理...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    # 1. 提取所有属性名和关系名
    print("正在提取属性名和关系名...")
    attribute_names, relation_names = extract_names_from_jsonl(input_file)
    print(f"提取到 {len(attribute_names)} 个属性名，{len(relation_names)} 个关系名")
    
    # 2. 过滤非标准化名称
    print("正在过滤非标准化名称...")
    non_standard_attributes, non_standard_relations = filter_non_standard_names(attribute_names, relation_names)
    print(f"需要标准化的属性名: {len(non_standard_attributes)} 个")
    print(f"需要标准化的关系名: {len(non_standard_relations)} 个")
    
    # 3. 批量标准化
    attribute_mapping = {}
    relation_mapping = {}
    
    if non_standard_attributes:
        print("正在标准化属性名...")
        attribute_mapping = standardize_attributes_batch(non_standard_attributes)
    
    if non_standard_relations:
        print("正在标准化关系名...")
        relation_mapping = standardize_relations_batch(non_standard_relations)
    
    # 4. 替换并保存
    print("正在替换名称并保存...")
    replace_names_in_jsonl(input_file, output_file, attribute_mapping, relation_mapping)
    
    # 5. 保存映射关系
    mapping_file = f"{output_dir}{MODEL_NAME}_mapping.json"
    mapping_data = {
        "attribute_mapping": attribute_mapping,
        "relation_mapping": relation_mapping,
        "standardized_time": datetime.now().isoformat()
    }
    
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(mapping_data, f, ensure_ascii=False, indent=2)
    
    print("标准化处理完成！")
    print(f"标准化结果: {output_file}")
    print(f"映射关系: {mapping_file}")

if __name__ == "__main__":
    main() 