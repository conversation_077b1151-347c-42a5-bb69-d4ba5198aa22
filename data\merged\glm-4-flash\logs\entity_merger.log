2025-08-01 12:07:49,346 - __main__ - INFO - ============================================================
2025-08-01 12:07:49,346 - __main__ - INFO - 实体合并工具启动
2025-08-01 12:07:49,347 - __main__ - INFO - ============================================================
2025-08-01 12:07:49,347 - __main__ - INFO - 验证输入文件: ../data/standardized_att_re/glm-4-flash/glm-4-flash_standardized.jsonl
2025-08-01 12:07:49,355 - __main__ - INFO - 创建输出目录: ../data/merged/glm-4-flash/
2025-08-01 12:07:49,356 - __main__ - INFO - 检测到断点继续模式
2025-08-01 12:07:49,356 - __main__ - WARNING - 未找到进度文件，将从头开始
2025-08-01 12:07:49,357 - __main__ - INFO - 初始化实体合并器
2025-08-01 12:07:49,358 - __main__ - ERROR - 执行过程中发生错误: Can't load tokenizer for './bert-base-chinese'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure './bert-base-chinese' is the correct path to a directory containing all relevant files for a BertTokenizer tokenizer.
2025-08-01 12:07:49,358 - __main__ - ERROR - 详细错误信息:
Traceback (most recent call last):
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\transformers\utils\hub.py", line 470, in cached_files
    hf_hub_download(
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\huggingface_hub\utils\_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\huggingface_hub\utils\_validators.py", line 160, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: './bert-base-chinese'.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\transformers\tokenization_utils_base.py", line 1923, in from_pretrained
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\transformers\utils\hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\transformers\utils\hub.py", line 523, in cached_files
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\transformers\utils\hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\huggingface_hub\utils\_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\huggingface_hub\utils\_validators.py", line 160, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: './bert-base-chinese'.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\code\work\pythonProject\fish\**********-llms-tripleExtraction-eva\entity_merger\run_entity_merge.py", line 148, in main
    merger = EntityMerger(
             ^^^^^^^^^^^^^
  File "E:\code\work\pythonProject\fish\**********-llms-tripleExtraction-eva\entity_merger\entity_merger\entity_merger.py", line 54, in __init__
    self.encoder = EntityEncoder(bert_model_path)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\code\work\pythonProject\fish\**********-llms-tripleExtraction-eva\entity_merger\entity_merger\entity_encoder.py", line 30, in __init__
    self.tokenizer = BertTokenizer.from_pretrained(model_path)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Anaconda3\envs\clint_py312\Lib\site-packages\transformers\tokenization_utils_base.py", line 1943, in from_pretrained
    raise OSError(
OSError: Can't load tokenizer for './bert-base-chinese'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure './bert-base-chinese' is the correct path to a directory containing all relevant files for a BertTokenizer tokenizer.
