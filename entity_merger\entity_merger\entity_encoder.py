# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 实体向量化模块，使用bert-base-chinese对实体进行编码
"""

import torch
from transformers import BertTokenizer, BertModel
import numpy as np
from typing import Dict, List, Tuple
import os
from tqdm import tqdm

class EntityEncoder:
    """实体向量化编码器"""
    
    def __init__(self, model_path: str = "./bert-base-chinese"):
        """
        初始化编码器
        
        Args:
            model_path: bert模型路径
        """
        self.model_path = model_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载模型和分词器
        print(f"正在加载BERT模型: {model_path}")
        self.tokenizer = BertTokenizer.from_pretrained(model_path)
        self.model = BertModel.from_pretrained(model_path)
        self.model.to(self.device)
        self.model.eval()
        
        print(f"BERT模型加载完成，使用设备: {self.device}")
    
    def encode_single_entity(self, entity_name: str) -> np.ndarray:
        """
        编码单个实体
        
        Args:
            entity_name: 实体名称
            
        Returns:
            实体向量表示
        """
        # 对输入进行编码
        inputs = self.tokenizer(
            entity_name,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=32
        )
        
        # 移动到设备
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # 获取向量表示
        with torch.no_grad():
            outputs = self.model(**inputs)
            # 使用[CLS]标记的输出作为实体表示
            entity_embedding = outputs.last_hidden_state[:, 0, :].cpu().numpy()
        
        return entity_embedding[0]  # 返回一维向量
    
    def encode_entities(self, entity_names: List[str]) -> Dict[str, np.ndarray]:
        """
        批量编码实体
        
        Args:
            entity_names: 实体名称列表
            
        Returns:
            实体名称到向量的映射字典
        """
        entity_embeddings = {}
        
        print(f"正在编码 {len(entity_names)} 个实体...")
        for entity_name in tqdm(entity_names, desc="编码实体"):
            try:
                embedding = self.encode_single_entity(entity_name)
                entity_embeddings[entity_name] = embedding
            except Exception as e:
                print(f"编码实体 '{entity_name}' 时出错: {e}")
                # 使用零向量作为默认值
                entity_embeddings[entity_name] = np.zeros(768)
        
        return entity_embeddings
    
    def get_entity_embeddings(self, entity_names: List[str]) -> Tuple[Dict[str, np.ndarray], List[str]]:
        """
        获取实体嵌入向量，并返回有效的实体名称列表
        
        Args:
            entity_names: 实体名称列表
            
        Returns:
            (实体嵌入字典, 有效实体名称列表)
        """
        # 去重
        unique_entities = list(set(entity_names))
        print(f"去重后共有 {len(unique_entities)} 个唯一实体")
        
        # 编码实体
        embeddings = self.encode_entities(unique_entities)
        
        # 过滤掉编码失败的实体
        valid_entities = []
        for entity_name in unique_entities:
            if entity_name in embeddings and not np.all(embeddings[entity_name] == 0):
                valid_entities.append(entity_name)
        
        print(f"成功编码 {len(valid_entities)} 个有效实体")
        
        return embeddings, valid_entities
    
    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            embedding1: 第一个向量
            embedding2: 第二个向量
            
        Returns:
            余弦相似度值 (0-1之间)
        """
        # 计算余弦相似度
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        similarity = dot_product / (norm1 * norm2)
        return float(similarity)
    
    def find_similar_pairs(self, embeddings: Dict[str, np.ndarray], 
                          threshold: float = 0.8) -> List[Tuple[str, str, float]]:
        """
        找到相似度超过阈值的实体对
        
        Args:
            embeddings: 实体嵌入字典
            threshold: 相似度阈值
            
        Returns:
            相似实体对列表，每个元素为 (实体1, 实体2, 相似度)
        """
        similar_pairs = []
        entity_names = list(embeddings.keys())
        
        print(f"正在查找相似度超过 {threshold} 的实体对...")
        
        for i in tqdm(range(len(entity_names)), desc="计算相似度"):
            for j in range(i + 1, len(entity_names)):
                entity1 = entity_names[i]
                entity2 = entity_names[j]
                
                similarity = self.compute_similarity(
                    embeddings[entity1], 
                    embeddings[entity2]
                )
                
                if similarity >= threshold:
                    similar_pairs.append((entity1, entity2, similarity))
        
        # 按相似度降序排序
        similar_pairs.sort(key=lambda x: x[2], reverse=True)
        
        print(f"找到 {len(similar_pairs)} 对相似实体")
        return similar_pairs 