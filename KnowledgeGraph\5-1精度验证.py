import spacy
import numpy as np
import time
import pandas as pd
import os

# 加载模型
nlp = spacy.load("zh_core_web_md")


# 从Excel读取三元组数据的函数
def read_triples_from_excel(file_path, sheet_name='Sheet1'):
    """从Excel文件读取三元组数据"""
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件未找到 - {file_path}")
        return []

    try:
        # 尝试读取Excel文件
        df = pd.read_excel(file_path, sheet_name=sheet_name)

        # 检查必要列是否存在
        required_columns = ['subject', 'predicate', 'object']
        for col in required_columns:
            if col not in df.columns:
                print(f"错误：Excel文件中缺少必要的列: '{col}'")
                return []

        triples = []
        for _, row in df.iterrows():
            s = str(row['subject']).strip()
            p = str(row['predicate']).strip()
            o = str(row['object']).strip()
            if s and p and o:  # 确保没有空值
                triples.append((s, p, o))
        return triples

    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return []


# 预计算所有唯一字符串的向量
def precompute_vectors(triples_list):
    unique_strings = set()
    for triples in triples_list:
        for triple in triples:
            unique_strings.update(triple)
    return {s: nlp(s).vector for s in unique_strings}


# 余弦相似度计算
def cosine_similarity(v1, v2):
    if np.all(v1 == 0) or np.all(v2 == 0):
        return 0.0
    dot_product = np.dot(v1, v2)
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    # 处理除零错误
    if norm_v1 == 0 or norm_v2 == 0:
        return 0.0
    return dot_product / (norm_v1 * norm_v2)


# 简化版的三元组匹配方案
def match_triples(predicted, true, vec_dict, verbose=False):
    # 存储匹配结果
    matched_pairs = []
    matched_true = set()
    matched_pred = set()

    start_time = time.time()
    print(f"开始匹配，预测三元组数: {len(predicted)}, 真实三元组数: {len(true)}")

    # 核心匹配逻辑
    for j, (true_s, true_p, true_o) in enumerate(true):
        # 跳过已匹配的真实三元组
        if j in matched_true:
            continue

        for i, (pred_s, pred_p, pred_o) in enumerate(predicted):
            # 跳过已匹配的预测三元组
            if i in matched_pred:
                continue

            # 计算各部分相似度
            s_sim = cosine_similarity(vec_dict[pred_s], vec_dict[true_s])
            p_sim = cosine_similarity(vec_dict[pred_p], vec_dict[true_p])
            o_sim = cosine_similarity(vec_dict[pred_o], vec_dict[true_o])

            # 分别设置主谓宾阈值
            if s_sim < 0.5:  # 主语要求高相似度
                continue
            if p_sim < 0.85:  # 谓语要求最高相似度
                continue
            if o_sim < 0.3:  # 宾语要求较宽松
                continue

            # 记录匹配
            matched_pairs.append((i, j))
            matched_true.add(j)
            matched_pred.add(i)

            if verbose:
                print(f"✅ 匹配三元组: \n  预测: ({pred_s}, {pred_p}, {pred_o})"
                      f"\n  标注: ({true_s}, {true_p}, {true_o})"
                      f"\n  相似度: s={s_sim:.2f}, p={p_sim:.2f}, o={o_sim:.2f}")
            break  # 每个真实三元组只匹配一个预测三元组

    print(f"匹配耗时: {time.time() - start_time:.2f}秒")
    return matched_pairs, matched_pred, matched_true


# 指标计算函数
def calculate_metrics(predicted, true, verbose=False):
    # 预计算词向量
    vec_dict = precompute_vectors([predicted, true])
    print(f"唯一字符串数量: {len(vec_dict)}")

    # 获取匹配结果
    matched_pairs, matched_pred, matched_true = match_triples(
        predicted, true, vec_dict, verbose
    )

    # 计算核心指标
    TP = len(matched_pairs)
    FP = len(predicted) - len(matched_pred)
    FN = len(true) - len(matched_true)

    precision = TP / (TP + FP) if TP + FP > 0 else 0
    recall = TP / (TP + FN) if TP + FN > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if precision + recall > 0 else 0

    if verbose:
        print(f"\n匹配统计: TP={TP}, FP={FP}, FN={FN}")
        print(f"未匹配预测三元组 ({FP}个):")
        for i, (s, p, o) in enumerate(predicted):
            if i not in matched_pred:
                print(f"  ({s}, {p}, {o})")

        print(f"\n未匹配真实三元组 ({FN}个):")
        for j, (s, p, o) in enumerate(true):
            if j not in matched_true:
                print(f"  ({s}, {p}, {o})")

    return precision, recall, f1


def main():
    # 定义Excel文件路径 - 使用原始字符串避免转义问题
    PREDICTED_EXCEL_PATH = r'D:\jingdu\gpt3.xlsx'
    MANUAL_EXCEL_PATH = r'D:\jingdu\rengong3.xlsx'

    # 从Excel文件读取数据
    predicted_triples = read_triples_from_excel(PREDICTED_EXCEL_PATH)
    true_triples = read_triples_from_excel(MANUAL_EXCEL_PATH)

    # 检查是否成功读取到数据
    if not predicted_triples:
        print("警告: 未能读取预测的三元组数据，请检查Excel文件路径和格式")
        return

    if not true_triples:
        print("警告: 未能读取真实的三元组数据，请检查Excel文件路径和格式")
        return

    print(f"成功读取预测三元组数量: {len(predicted_triples)}")
    print(f"成功读取真实三元组数量: {len(true_triples)}")

    # 计算指标
    print("=" * 50)
    print("评估开始 (基于相似度的三元组匹配评估)")
    precision, recall, f1 = calculate_metrics(
        predicted_triples,
        true_triples,
        verbose=True
    )

    # 输出结果
    print(f"\n评估结果:")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    print(f"F1-Score: {f1:.4f}")
    print("=" * 50)


if __name__ == "__main__":
    main()