# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 主控制模块，整合所有功能实现完整的实体合并流程
"""

import json
import os
from typing import Dict, List, Tuple
from datetime import datetime
import pickle

from .entity_encoder import EntityEncoder
from .entity_clusterer import EntityClusterer
from .entity_judge import EntityJudge
from .data_updater import DataUpdater

class EntityMerger:
    """实体合并器主控制类"""
    
    def __init__(self,
                 base_url: str,
                 api_key: str,
                 model_name: str = "glm-4-flash",
                 bert_model_path: str = "./bert-base-chinese",
                 similarity_threshold: float = 0.8,
                 max_iterations: int = 5,
                 max_candidates_per_iteration: int = 50,
                 progress_dir: str = "./progress"):
        """
        初始化实体合并器

        Args:
            base_url: LLM API基础URL
            api_key: LLM API密钥
            model_name: LLM模型名称
            bert_model_path: BERT模型路径
            similarity_threshold: 相似度阈值
            max_iterations: 最大迭代轮数
            max_candidates_per_iteration: 每轮最大候选对数
            progress_dir: 进度保存目录
        """
        self.base_url = base_url
        self.api_key = api_key
        self.model_name = model_name
        self.bert_model_path = bert_model_path
        self.similarity_threshold = similarity_threshold
        self.max_iterations = max_iterations
        self.max_candidates_per_iteration = max_candidates_per_iteration

        # 初始化各个模块
        print("正在初始化实体合并器...")
        self.encoder = EntityEncoder(bert_model_path)
        self.clusterer = EntityClusterer(similarity_threshold)
        self.judge = EntityJudge(base_url, api_key, model_name)
        self.updater = DataUpdater()

        # 进度保存路径
        self.progress_dir = progress_dir
        os.makedirs(self.progress_dir, exist_ok=True)

        print("实体合并器初始化完成")
    
    def extract_all_entities(self, input_file: str) -> Tuple[List[str], Dict[str, str]]:
        """
        从输入文件中提取所有实体
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            (实体名称列表, 实体类型字典)
        """
        print("正在提取所有实体...")
        
        # 提取实体类型信息
        entity_types = self.updater.extract_entities_from_jsonl(input_file)
        
        # 获取实体名称列表
        entity_names = list(entity_types.keys())
        
        print(f"提取到 {len(entity_names)} 个实体")
        return entity_names, entity_types
    
    def run_single_iteration(self, 
                           input_file: str,
                           iteration: int,
                           entity_types: Dict[str, str]) -> Tuple[Dict[str, str], int]:
        """
        运行单轮合并迭代
        
        Args:
            input_file: 输入文件路径
            iteration: 当前迭代轮数
            entity_types: 实体类型字典
            
        Returns:
            (合并映射字典, 合并的实体对数)
        """
        print(f"\n=== 开始第 {iteration} 轮合并迭代 ===")
        
        # 1. 提取实体
        entity_names, _ = self.extract_all_entities(input_file)
        
        if len(entity_names) < 2:
            print("实体数量不足，无法进行合并")
            return {}, 0
        
        # 2. 编码实体
        print("正在编码实体...")
        embeddings, valid_entities = self.encoder.get_entity_embeddings(entity_names)
        
        if len(valid_entities) < 2:
            print("有效实体数量不足，无法进行合并")
            return {}, 0
        
        # 3. 找到相似实体对
        print("正在查找相似实体对...")
        similar_pairs = self.clusterer.get_merge_candidates(
            embeddings, 
            self.max_candidates_per_iteration
        )
        
        if not similar_pairs:
            print("没有找到相似实体对")
            return {}, 0
        
        # 4. LLM判断是否合并
        print("正在使用LLM判断合并候选...")
        merge_decisions = self.judge.judge_merge_candidates(similar_pairs)
        
        # 验证决策
        valid_decisions = self.judge.validate_merge_decisions(merge_decisions)
        
        # 过滤出应该合并的决策
        merge_decisions = self.judge.filter_merge_decisions(valid_decisions)
        
        if not merge_decisions:
            print("LLM认为没有实体需要合并")
            return {}, 0
        
        # 5. 生成合并映射
        merge_mapping = self.judge.get_merge_mapping(merge_decisions)
        
        print(f"第 {iteration} 轮合并完成，共合并 {len(merge_mapping)} 个实体")
        
        # 6. 保存中间结果
        self.save_iteration_results(iteration, merge_decisions, merge_mapping)
        
        return merge_mapping, len(merge_decisions)
    
    def save_iteration_results(self, iteration: int, decisions: List[Dict], 
                             mapping: Dict[str, str]):
        """保存迭代结果"""
        # 保存决策
        decisions_file = f"{self.progress_dir}/iteration_{iteration}_decisions.json"
        self.judge.save_merge_decisions(decisions, decisions_file)
        
        # 保存映射
        mapping_file = f"{self.progress_dir}/iteration_{iteration}_mapping.json"
        self.updater.save_merge_mapping(mapping, mapping_file)
        
        # 保存进度
        progress_file = f"{self.progress_dir}/merge_progress.pkl"
        progress_data = {
            "current_iteration": iteration,
            "total_decisions": len(decisions),
            "total_mappings": len(mapping),
            "timestamp": datetime.now().isoformat()
        }
        
        with open(progress_file, 'wb') as f:
            pickle.dump(progress_data, f)
    
    def load_progress(self) -> Dict:
        """加载进度信息"""
        progress_file = f"{self.progress_dir}/merge_progress.pkl"
        
        if os.path.exists(progress_file):
            with open(progress_file, 'rb') as f:
                return pickle.load(f)
        
        return {"current_iteration": 0, "total_decisions": 0, "total_mappings": 0}
    
    def merge_entities(self, input_file: str, output_dir: str, resume: bool = False) -> Dict:
        """
        执行完整的实体合并流程

        Args:
            input_file: 输入文件路径
            output_dir: 输出目录路径
            resume: 是否从断点继续

        Returns:
            合并结果统计
        """
        print("开始实体合并流程...")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 根据resume参数决定是否加载进度
        if resume:
            progress = self.load_progress()
            start_iteration = progress["current_iteration"] + 1
            print(f"从第 {start_iteration} 轮继续执行")
        else:
            progress = {"current_iteration": 0, "total_decisions": 0, "total_mappings": 0}
            start_iteration = 1
            print("从头开始执行")

        if start_iteration > self.max_iterations:
            print(f"已达到最大迭代轮数 {self.max_iterations}")
            return progress
        
        # 提取实体类型信息
        entity_types = self.updater.extract_entities_from_jsonl(input_file)
        original_entity_count = len(entity_types)

        # 创建备份
        backup_file = self.updater.create_backup(input_file)
        
        # 初始化当前输入文件
        current_input_file = input_file
        total_merged_pairs = 0
        cumulative_mapping = {}
        
        # 执行迭代合并
        for iteration in range(start_iteration, self.max_iterations + 1):
            print(f"\n{'='*50}")
            print(f"第 {iteration} 轮合并迭代")
            print(f"{'='*50}")
            
            # 运行单轮迭代
            merge_mapping, merged_pairs = self.run_single_iteration(
                current_input_file, iteration, entity_types
            )
            
            if not merge_mapping:
                print(f"第 {iteration} 轮没有实体需要合并，停止迭代")
                break
            
            # 更新累计映射
            cumulative_mapping.update(merge_mapping)
            total_merged_pairs += merged_pairs
            
            # 更新数据文件
            current_output_file = f"{output_dir}/merged_iteration_{iteration}.jsonl"
            updated_count = self.updater.update_jsonl_file(
                current_input_file, current_output_file, merge_mapping
            )
            
            print(f"第 {iteration} 轮更新了 {updated_count} 条记录")
            
            # 更新当前输入文件为输出文件
            current_input_file = current_output_file
            
            # 检查是否还有可合并的实体
            remaining_entities, _ = self.extract_all_entities(current_input_file)
            print(f"剩余实体数量: {len(remaining_entities)}")
            
            if len(remaining_entities) < 2:
                print("剩余实体数量不足，停止迭代")
                break
        
        # 保存最终结果
        final_mapping_file = f"{output_dir}/final_merge_mapping.json"
        self.updater.save_merge_mapping(cumulative_mapping, final_mapping_file)
        
        # 生成统计信息
        statistics = self.updater.get_merge_statistics(cumulative_mapping)
        
        # 验证结果
        validation_results = self.updater.validate_merge_mapping(cumulative_mapping, entity_types)
        
        # 生成最终报告
        final_report = {
            "iterations": iteration,
            "merge_pairs": total_merged_pairs,
            "total_mappings": len(cumulative_mapping),
            "original_entities": original_entity_count,
            "merged_entities": original_entity_count - len(cumulative_mapping),
            "output_file": current_input_file,
            "statistics": statistics,
            "validation": validation_results,
            "backup_file": backup_file,
            "final_output_file": current_input_file,
            "completion_time": datetime.now().isoformat()
        }
        
        # 保存最终报告
        report_file = f"{output_dir}/merge_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n{'='*50}")
        print("实体合并流程完成")
        print(f"{'='*50}")
        print(f"总迭代轮数: {iteration}")
        print(f"总合并对数: {total_merged_pairs}")
        print(f"总映射数量: {len(cumulative_mapping)}")
        print(f"最终输出文件: {current_input_file}")
        print(f"合并报告: {report_file}")
        
        return final_report
    
    def resume_merge(self, input_file: str, output_dir: str) -> Dict:
        """
        从断点恢复合并流程
        
        Args:
            input_file: 输入文件路径
            output_dir: 输出目录路径
            
        Returns:
            合并结果统计
        """
        print("从断点恢复合并流程...")
        
        progress = self.load_progress()
        print(f"当前进度: 第 {progress['current_iteration']} 轮，"
              f"已合并 {progress['total_mappings']} 个实体")
        
        return self.merge_entities(input_file, output_dir)

def main():
    """主函数"""
    # 配置参数
    BASE_URL = "https://open.bigmodel.cn/api/paas/v4"
    API_KEY = "fdd92b30af6c46efb5d18e0eb6540816.c69FP2JZnAAMRzia"
    MODEL_NAME = "glm-4-flash"
    
    # 文件路径
    input_file = "./standardized/glm-4-flash/glm-4-flash_standardized.jsonl"
    output_dir = "./merged/glm-4-flash/"
    
    # 创建实体合并器
    merger = EntityMerger(
        base_url=BASE_URL,
        api_key=API_KEY,
        model_name=MODEL_NAME,
        similarity_threshold=0.8,
        max_iterations=5,
        max_candidates_per_iteration=50
    )
    
    # 执行合并
    try:
        result = merger.merge_entities(input_file, output_dir)
        print("实体合并成功完成！")
        
    except Exception as e:
        print(f"实体合并过程中出错: {e}")
        print("可以调用 merger.resume_merge() 从断点恢复")

if __name__ == "__main__":
    main() 