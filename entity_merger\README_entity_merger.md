# 实体合并功能使用文档

## 概述

实体合并功能是一个基于 BERT 向量化和 LLM 判断的智能实体合并系统，能够自动识别和合并相似的实体，提高知识图谱的质量和一致性。

## 功能特点

- **智能相似度计算**: 使用 BERT-base-chinese 模型计算实体语义相似度
- **LLM 智能判断**: 利用大语言模型判断实体是否应该合并
- **渐进式迭代**: 支持多轮迭代，逐步优化合并结果
- **断点继续**: 支持中断后继续处理，避免重复计算
- **进度监控**: 实时监控处理进度和状态
- **结果验证**: 自动验证合并结果的正确性和一致性
- **灵活配置**: 支持多种参数配置和自定义设置

## 系统架构

```
实体合并系统
├── 实体向量化模块 (entity_encoder.py)
├── 实体聚类模块 (entity_clusterer.py)
├── LLM判断模块 (entity_judge.py)
├── 数据更新模块 (data_updater.py)
├── 主控制模块 (entity_merger.py)
├── 进度监控模块 (progress_monitor.py)
├── 结果验证模块 (result_validator.py)
└── 配置管理 (config.py)
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 基本使用

```python
from entity_merger import EntityMerger
from config import LLM_CONFIG, BERT_CONFIG, MERGE_CONFIG, FILE_CONFIG

# 初始化实体合并器
merger = EntityMerger(
    base_url=LLM_CONFIG['base_url'],
    api_key=LLM_CONFIG['api_key'],
    model_name=LLM_CONFIG['model_name'],
    bert_model_path=BERT_CONFIG['model_path'],
    similarity_threshold=MERGE_CONFIG['similarity_threshold'],
    max_iterations=MERGE_CONFIG['max_iterations'],
    max_candidates_per_iteration=MERGE_CONFIG['max_candidates_per_iteration']
)

# 执行实体合并
result = merger.merge_entities(
    input_file=FILE_CONFIG['input_file'],
    output_dir=FILE_CONFIG['output_dir']
)

print(f"合并完成！")
print(f"原始实体数量: {result['original_entities']}")
print(f"合并后实体数量: {result['merged_entities']}")
print(f"合并实体对数量: {result['merge_pairs']}")
```

### 2. 命令行使用

```bash
# 基本使用
python run_entity_merger.py

# 自定义参数
python run_entity_merger.py --similarity-threshold 0.9 --max-iterations 10

# 断点继续
python run_entity_merger.py --resume

# 试运行模式
python run_entity_merger.py --dry-run

# 详细输出
python run_entity_merger.py --verbose
```

## 配置说明

### 配置文件 (config.py)

```python
# LLM配置
LLM_CONFIG = {
    "base_url": "https://open.bigmodel.cn/api/paas/v4",
    "api_key": "your_api_key_here",
    "model_name": "glm-4-flash",
    "temperature": 0.1
}

# BERT模型配置
BERT_CONFIG = {
    "model_path": "./bert-base-chinese",
    "max_length": 32,
    "batch_size": 16
}

# 实体合并配置
MERGE_CONFIG = {
    "similarity_threshold": 0.8,  # 相似度阈值
    "max_iterations": 5,  # 最大迭代轮数
    "max_candidates_per_iteration": 50,  # 每轮最大候选对数
    "batch_size": 10,  # LLM批处理大小
    "progress_save_interval": 1  # 进度保存间隔（轮数）
}

# 文件路径配置
FILE_CONFIG = {
    "input_file": "./standardized/glm-4-flash/glm-4-flash_standardized.jsonl",
    "output_dir": "./merged/glm-4-flash/",
    "progress_dir": "./progress",
    "backup_dir": "./backup"
}
```

### 参数说明

| 参数                         | 类型  | 默认值 | 说明                   |
| ---------------------------- | ----- | ------ | ---------------------- |
| similarity_threshold         | float | 0.8    | 相似度阈值，越高越严格 |
| max_iterations               | int   | 5      | 最大迭代轮数           |
| max_candidates_per_iteration | int   | 50     | 每轮最大候选实体对数   |
| batch_size                   | int   | 10     | LLM 批处理大小         |
| progress_save_interval       | int   | 1      | 进度保存间隔           |

## 使用示例

### 示例 1: 基本使用

```python
from example_usage import example_basic_usage

# 运行基本使用示例
example_basic_usage()
```

### 示例 2: 带进度监控

```python
from example_usage import example_with_progress_monitoring

# 运行带进度监控的示例
example_with_progress_monitoring()
```

### 示例 3: 带结果验证

```python
from example_usage import example_with_validation

# 运行带结果验证的示例
example_with_validation()
```

### 示例 4: 试运行模式

```python
from example_usage import example_dry_run

# 运行试运行示例
example_dry_run()
```

## 数据格式

### 输入数据格式

输入数据应为 JSONL 格式，每行包含一个 JSON 对象：

```json
{
  "id": "1",
  "text": "文本内容",
  "entity_attribute": [
    {
      "name": "实体名称",
      "type": "实体类型",
      "attribute": [
        {
          "name": "属性名称",
          "value": "属性值"
        }
      ]
    }
  ],
  "entity_relation": [
    {
      "source_entity": "源实体",
      "source_entity_type": "源实体类型",
      "target_entity": "目标实体",
      "target_entity_type": "目标实体类型",
      "relationship": "关系类型"
    }
  ]
}
```

### 输出数据格式

输出数据格式与输入相同，但实体可能已被合并：

```json
{
  "id": "1",
  "text": "文本内容",
  "entity_attribute": [
    {
      "name": "合并后的实体名称",
      "type": "实体类型",
      "attribute": [
        {
          "name": "属性名称",
          "value": "属性值"
        }
      ]
    }
  ],
  "entity_relation": [
    {
      "source_entity": "合并后的源实体",
      "source_entity_type": "源实体类型",
      "target_entity": "合并后的目标实体",
      "target_entity_type": "目标实体类型",
      "relationship": "关系类型"
    }
  ],
  "merge_info": {
    "merged_entities": ["原实体1", "原实体2"],
    "merge_time": "2025-01-01T12:00:00"
  }
}
```

## 处理流程

1. **数据加载**: 加载标准化的实体数据
2. **实体提取**: 提取所有实体及其属性
3. **向量化**: 使用 BERT 模型对实体进行向量化
4. **相似度计算**: 计算实体间的相似度矩阵
5. **聚类分组**: 基于相似度阈值进行实体分组
6. **LLM 判断**: 使用大语言模型判断是否合并
7. **数据更新**: 更新实体名称和关系
8. **迭代优化**: 重复步骤 4-7 直到达到终止条件
9. **结果输出**: 保存合并后的数据

## 进度监控

系统提供完整的进度监控功能：

```python
from progress_monitor import ProgressMonitor

# 初始化进度监控器
progress_monitor = ProgressMonitor(
    progress_dir="./progress",
    model_name="glm-4-flash"
)

# 检查是否有现有进度
if progress_monitor.has_progress():
    print("发现现有进度，可以继续处理")
    resume_info = progress_monitor.get_resume_info()
    print(f"当前迭代轮数: {resume_info['current_iteration']}")

# 打印当前进度
progress_monitor.print_progress()
```

## 结果验证

系统提供自动结果验证功能：

```python
from result_validator import ResultValidator

# 初始化结果验证器
validator = ResultValidator()

# 验证合并结果
validation_result = validator.validate_merge_result(
    original_file="original.jsonl",
    merged_file="merged.jsonl",
    merge_stats=result
)

# 打印验证摘要
validator.print_validation_summary(validation_result)
```

## 错误处理

系统提供完善的错误处理机制：

```python
try:
    result = merger.merge_entities(
        input_file="input.jsonl",
        output_dir="./output"
    )
except FileNotFoundError as e:
    print(f"文件不存在: {e}")
except Exception as e:
    print(f"处理错误: {e}")
```

## 性能优化

### 1. 参数调优

- **相似度阈值**: 根据数据特点调整，一般 0.7-0.9
- **批处理大小**: 根据内存和 API 限制调整
- **迭代轮数**: 根据数据规模和合并需求调整

### 2. 内存优化

- 使用生成器处理大文件
- 及时释放不需要的数据
- 使用 pickle 保存中间结果

### 3. API 优化

- 批量处理减少 API 调用
- 添加重试机制
- 使用缓存避免重复计算

## 常见问题

### Q1: 如何处理大文件？

A: 系统支持断点继续功能，可以分批次处理大文件。建议设置合适的批处理大小和进度保存间隔。

### Q2: 如何调整合并的严格程度？

A: 通过调整`similarity_threshold`参数控制合并的严格程度。值越高，合并越严格。

### Q3: 如何处理 API 限制？

A: 系统内置了重试机制和批处理功能，可以有效处理 API 限制。也可以调整`batch_size`参数。

### Q4: 如何验证合并结果？

A: 使用`ResultValidator`类可以自动验证合并结果的正确性和一致性。

### Q5: 如何自定义合并规则？

A: 可以修改`entity_judge.py`中的提示词模板来自定义合并规则。

## 故障排除

### 1. 内存不足

- 减少批处理大小
- 使用试运行模式分析数据规模
- 增加进度保存频率

### 2. API 错误

- 检查 API 密钥和 URL 配置
- 增加重试次数和间隔
- 检查网络连接

### 3. 文件格式错误

- 检查输入文件格式
- 确保 JSON 格式正确
- 检查编码格式

### 4. 模型加载失败

- 检查 BERT 模型路径
- 确保模型文件完整
- 检查依赖包版本

## 更新日志

### v1.0.0 (2025-01-01)

- 初始版本发布
- 支持基本的实体合并功能
- 提供进度监控和结果验证

## 技术支持

如有问题，请参考：

1. 本文档
2. 示例代码
3. 配置文件说明
4. 错误日志

## 许可证

本项目采用 MIT 许可证。
