# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 实体合并主执行脚本
"""

import argparse
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加entity_merger目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'entity_merger'))

from entity_merger.entity_merger import EntityMerger
from entity_merger.config import LLM_CONFIG, BERT_CONFIG, MERGE_CONFIG, FILE_CONFIG
from entity_merger.progress_monitor import ProgressMonitor

def setup_logging(log_level=logging.INFO):
    """设置日志配置"""
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('entity_merger.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='实体合并工具')
    
    # 输入输出参数
    parser.add_argument('--input', type=str, 
                       default=FILE_CONFIG['input_file'],
                       help='输入文件路径')
    parser.add_argument('--output-dir', type=str,
                       default=FILE_CONFIG['output_dir'],
                       help='输出目录路径')
    
    # 模型参数
    parser.add_argument('--similarity-threshold', type=float,
                       default=MERGE_CONFIG['similarity_threshold'],
                       help='相似度阈值')
    parser.add_argument('--max-iterations', type=int,
                       default=MERGE_CONFIG['max_iterations'],
                       help='最大迭代轮数')
    parser.add_argument('--max-candidates', type=int,
                       default=MERGE_CONFIG['max_candidates_per_iteration'],
                       help='每轮最大候选对数')
    
    # LLM参数
    parser.add_argument('--base-url', type=str,
                       default=LLM_CONFIG['base_url'],
                       help='LLM API基础URL')
    parser.add_argument('--api-key', type=str,
                       default=LLM_CONFIG['api_key'],
                       help='LLM API密钥')
    parser.add_argument('--model-name', type=str,
                       default=LLM_CONFIG['model_name'],
                       help='LLM模型名称')
    
    # BERT参数
    parser.add_argument('--bert-model-path', type=str,
                       default=BERT_CONFIG['model_path'],
                       help='BERT模型路径')
    
    # 其他参数
    parser.add_argument('--resume', action='store_true',
                       help='是否从断点继续')
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出模式')
    
    return parser.parse_args()

def validate_input_file(input_file):
    """验证输入文件"""
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"输入文件不存在: {input_file}")
    
    if not input_file.endswith('.jsonl'):
        raise ValueError(f"输入文件必须是JSONL格式: {input_file}")
    
    # 检查文件是否为空
    with open(input_file, 'r', encoding='utf-8') as f:
        first_line = f.readline().strip()
        if not first_line:
            raise ValueError(f"输入文件为空: {input_file}")

def create_output_dirs(output_dir):
    """创建输出目录"""
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'logs'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'backup'), exist_ok=True)

def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logging(log_level)
    
    logger.info("=" * 60)
    logger.info("实体合并工具启动")
    logger.info("=" * 60)
    
    try:
        # 验证输入文件
        logger.info(f"验证输入文件: {args.input}")
        validate_input_file(args.input)
        
        # 创建输出目录
        logger.info(f"创建输出目录: {args.output_dir}")
        create_output_dirs(args.output_dir)
        
        # 初始化进度监控器
        progress_monitor = ProgressMonitor(
            progress_dir=FILE_CONFIG['progress_dir'],
            model_name=args.model_name
        )
        
        # 检查是否需要从断点继续
        if args.resume:
            logger.info("检测到断点继续模式")
            if not progress_monitor.has_progress():
                logger.warning("未找到进度文件，将从头开始")
                args.resume = False
        
        # 初始化实体合并器
        logger.info("初始化实体合并器")
        merger = EntityMerger(
            base_url=args.base_url,
            api_key=args.api_key,
            model_name=args.model_name,
            bert_model_path=args.bert_model_path,
            similarity_threshold=args.similarity_threshold,
            max_iterations=args.max_iterations,
            max_candidates_per_iteration=args.max_candidates
        )
        
        # 执行实体合并
        logger.info("开始执行实体合并")
        result = merger.merge_entities(
            input_file=args.input,
            output_dir=args.output_dir,
            resume=args.resume
        )
        
        # 输出结果统计
        logger.info("=" * 60)
        logger.info("实体合并完成")
        logger.info("=" * 60)
        logger.info(f"原始实体数量: {result['original_entities']}")
        logger.info(f"合并后实体数量: {result['merged_entities']}")
        logger.info(f"合并实体对数量: {result['merge_pairs']}")
        logger.info(f"处理轮数: {result['iterations']}")
        logger.info(f"输出文件: {result['output_file']}")
        
        # 保存结果统计
        stats_file = os.path.join(args.output_dir, 'merge_statistics.json')
        import json
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logger.info(f"统计结果已保存到: {stats_file}")
        
        logger.info("实体合并工具执行完成")
        
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        sys.exit(1)

if __name__ == "__main__":
    main() 