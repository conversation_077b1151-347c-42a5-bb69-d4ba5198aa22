2025-08-01 12:07:15,428 - __main__ - INFO - ============================================================
2025-08-01 12:07:15,428 - __main__ - INFO - 实体合并工具启动
2025-08-01 12:07:15,428 - __main__ - INFO - ============================================================
2025-08-01 12:07:15,429 - __main__ - INFO - 验证输入文件: ./data/standardized_att_re/glm-4-flash/glm-4-flash_standardized.jsonl
2025-08-01 12:07:15,429 - __main__ - ERROR - 执行过程中发生错误: 输入文件不存在: ./data/standardized_att_re/glm-4-flash/glm-4-flash_standardized.jsonl
2025-08-01 12:07:15,429 - __main__ - ERROR - 详细错误信息:
Traceback (most recent call last):
  File "E:\code\work\pythonProject\fish\A-20250802-llms-tripleExtraction-eva\entity_merger\run_entity_merge.py", line 124, in main
    validate_input_file(args.input)
  File "E:\code\work\pythonProject\fish\A-20250802-llms-tripleExtraction-eva\entity_merger\run_entity_merge.py", line 90, in validate_input_file
    raise FileNotFoundError(f"输入文件不存在: {input_file}")
FileNotFoundError: 输入文件不存在: ./data/standardized_att_re/glm-4-flash/glm-4-flash_standardized.jsonl
