# -*- coding: utf-8 -*-
"""
@author: 
@date: 2025
@description: 结果验证模块，验证合并结果的正确性和一致性
"""

import json
import os
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict, Counter
import logging

logger = logging.getLogger(__name__)

class ResultValidator:
    """结果验证器"""
    
    def __init__(self):
        """初始化结果验证器"""
        pass
    
    def validate_merge_result(self, original_file: str, merged_file: str, 
                            merge_stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证合并结果
        
        Args:
            original_file: 原始文件路径
            merged_file: 合并后文件路径
            merge_stats: 合并统计信息
            
        Returns:
            验证结果字典
        """
        logger.info("开始验证合并结果")
        
        validation_result = {
            'validation_time': None,
            'overall_status': 'unknown',
            'checks': {},
            'warnings': [],
            'errors': []
        }
        
        try:
            # 加载原始数据
            original_data = self._load_jsonl_file(original_file)
            logger.info(f"加载原始数据: {len(original_data)} 条记录")
            
            # 加载合并后数据
            merged_data = self._load_jsonl_file(merged_file)
            logger.info(f"加载合并后数据: {len(merged_data)} 条记录")
            
            # 执行各项验证
            validation_result['checks'] = {
                'data_integrity': self._check_data_integrity(original_data, merged_data),
                'entity_count': self._check_entity_count(original_data, merged_data, merge_stats),
                'entity_consistency': self._check_entity_consistency(original_data, merged_data),
                'relation_integrity': self._check_relation_integrity(original_data, merged_data),
                'attribute_integrity': self._check_attribute_integrity(original_data, merged_data),
                'merge_consistency': self._check_merge_consistency(merged_data, merge_stats)
            }
            
            # 评估整体状态
            validation_result['overall_status'] = self._evaluate_overall_status(validation_result['checks'])
            
            # 生成验证报告
            self._generate_validation_report(validation_result, original_file, merged_file)
            
            logger.info(f"验证完成，整体状态: {validation_result['overall_status']}")
            
        except Exception as e:
            logger.error(f"验证过程中发生错误: {e}")
            validation_result['errors'].append(str(e))
            validation_result['overall_status'] = 'error'
        
        return validation_result
    
    def _load_jsonl_file(self, file_path: str) -> List[Dict]:
        """加载JSONL文件"""
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    data.append(json.loads(line.strip()))
        return data
    
    def _check_data_integrity(self, original_data: List[Dict], merged_data: List[Dict]) -> Dict[str, Any]:
        """检查数据完整性"""
        result = {
            'status': 'unknown',
            'details': {},
            'warnings': []
        }
        
        # 检查记录数量
        original_count = len(original_data)
        merged_count = len(merged_data)
        
        if original_count != merged_count:
            result['warnings'].append(f"记录数量不匹配: 原始 {original_count}, 合并后 {merged_count}")
            result['status'] = 'warning'
        else:
            result['status'] = 'pass'
        
        result['details'] = {
            'original_record_count': original_count,
            'merged_record_count': merged_count
        }
        
        return result
    
    def _check_entity_count(self, original_data: List[Dict], merged_data: List[Dict], 
                          merge_stats: Dict[str, Any]) -> Dict[str, Any]:
        """检查实体数量"""
        result = {
            'status': 'unknown',
            'details': {},
            'warnings': []
        }
        
        # 统计原始实体数量
        original_entities = set()
        for record in original_data:
            if 'entity_attribute' in record:
                for entity in record['entity_attribute']:
                    original_entities.add(entity['name'])
        
        # 统计合并后实体数量
        merged_entities = set()
        for record in merged_data:
            if 'entity_attribute' in record:
                for entity in record['entity_attribute']:
                    merged_entities.add(entity['name'])
        
        # 检查实体数量变化
        original_count = len(original_entities)
        merged_count = len(merged_entities)
        expected_merged_count = original_count - merge_stats.get('merge_pairs', 0)
        
        result['details'] = {
            'original_entity_count': original_count,
            'merged_entity_count': merged_count,
            'expected_merged_count': expected_merged_count,
            'merge_pairs': merge_stats.get('merge_pairs', 0)
        }
        
        if merged_count != expected_merged_count:
            result['warnings'].append(
                f"实体数量不符合预期: 实际 {merged_count}, 预期 {expected_merged_count}"
            )
            result['status'] = 'warning'
        else:
            result['status'] = 'pass'
        
        return result
    
    def _check_entity_consistency(self, original_data: List[Dict], merged_data: List[Dict]) -> Dict[str, Any]:
        """检查实体一致性"""
        result = {
            'status': 'unknown',
            'details': {},
            'warnings': []
        }
        
        # 检查实体类型分布
        original_types = Counter()
        merged_types = Counter()
        
        for record in original_data:
            if 'entity_attribute' in record:
                for entity in record['entity_attribute']:
                    original_types[entity['type']] += 1
        
        for record in merged_data:
            if 'entity_attribute' in record:
                for entity in record['entity_attribute']:
                    merged_types[entity['type']] += 1
        
        # 检查类型变化
        type_changes = {}
        all_types = set(original_types.keys()) | set(merged_types.keys())
        
        for entity_type in all_types:
            original_count = original_types.get(entity_type, 0)
            merged_count = merged_types.get(entity_type, 0)
            if original_count != merged_count:
                type_changes[entity_type] = {
                    'original': original_count,
                    'merged': merged_count,
                    'change': merged_count - original_count
                }
        
        result['details'] = {
            'original_type_distribution': dict(original_types),
            'merged_type_distribution': dict(merged_types),
            'type_changes': type_changes
        }
        
        if type_changes:
            result['warnings'].append(f"发现 {len(type_changes)} 个实体类型数量发生变化")
            result['status'] = 'warning'
        else:
            result['status'] = 'pass'
        
        return result
    
    def _check_relation_integrity(self, original_data: List[Dict], merged_data: List[Dict]) -> Dict[str, Any]:
        """检查关系完整性"""
        result = {
            'status': 'unknown',
            'details': {},
            'warnings': []
        }
        
        # 统计关系数量
        original_relations = 0
        merged_relations = 0
        
        for record in original_data:
            if 'entity_relation' in record:
                original_relations += len(record['entity_relation'])
        
        for record in merged_data:
            if 'entity_relation' in record:
                merged_relations += len(record['entity_relation'])
        
        result['details'] = {
            'original_relation_count': original_relations,
            'merged_relation_count': merged_relations
        }
        
        # 检查关系数量是否合理
        if merged_relations < original_relations * 0.8:  # 允许20%的减少
            result['warnings'].append(f"关系数量显著减少: 原始 {original_relations}, 合并后 {merged_relations}")
            result['status'] = 'warning'
        else:
            result['status'] = 'pass'
        
        return result
    
    def _check_attribute_integrity(self, original_data: List[Dict], merged_data: List[Dict]) -> Dict[str, Any]:
        """检查属性完整性"""
        result = {
            'status': 'unknown',
            'details': {},
            'warnings': []
        }
        
        # 统计属性数量
        original_attributes = 0
        merged_attributes = 0
        
        for record in original_data:
            if 'entity_attribute' in record:
                for entity in record['entity_attribute']:
                    if 'attribute' in entity:
                        original_attributes += len(entity['attribute'])
        
        for record in merged_data:
            if 'entity_attribute' in record:
                for entity in record['entity_attribute']:
                    if 'attribute' in entity:
                        merged_attributes += len(entity['attribute'])
        
        result['details'] = {
            'original_attribute_count': original_attributes,
            'merged_attribute_count': merged_attributes
        }
        
        # 检查属性数量是否合理
        if merged_attributes < original_attributes * 0.9:  # 允许10%的减少
            result['warnings'].append(f"属性数量显著减少: 原始 {original_attributes}, 合并后 {merged_attributes}")
            result['status'] = 'warning'
        else:
            result['status'] = 'pass'
        
        return result
    
    def _check_merge_consistency(self, merged_data: List[Dict], merge_stats: Dict[str, Any]) -> Dict[str, Any]:
        """检查合并一致性"""
        result = {
            'status': 'unknown',
            'details': {},
            'warnings': []
        }
        
        # 检查合并后的实体名称是否合理
        entity_names = set()
        suspicious_names = []
        
        for record in merged_data:
            if 'entity_attribute' in record:
                for entity in record['entity_attribute']:
                    name = entity['name']
                    entity_names.add(name)
                    
                    # 检查可疑的实体名称
                    if len(name) < 2:
                        suspicious_names.append(name)
                    elif name.count(' ') > 3:  # 空格过多
                        suspicious_names.append(name)
        
        result['details'] = {
            'total_entities': len(entity_names),
            'suspicious_names': suspicious_names
        }
        
        if suspicious_names:
            result['warnings'].append(f"发现 {len(suspicious_names)} 个可疑的实体名称")
            result['status'] = 'warning'
        else:
            result['status'] = 'pass'
        
        return result
    
    def _evaluate_overall_status(self, checks: Dict[str, Dict]) -> str:
        """评估整体状态"""
        status_counts = Counter()
        
        for check_name, check_result in checks.items():
            status_counts[check_result['status']] += 1
        
        if status_counts['error'] > 0:
            return 'error'
        elif status_counts['warning'] > 0:
            return 'warning'
        elif status_counts['pass'] == len(checks):
            return 'pass'
        else:
            return 'unknown'
    
    def _generate_validation_report(self, validation_result: Dict[str, Any], 
                                  original_file: str, merged_file: str):
        """生成验证报告"""
        report_file = merged_file.replace('.jsonl', '_validation_report.json')
        
        report = {
            'validation_time': validation_result.get('validation_time'),
            'original_file': original_file,
            'merged_file': merged_file,
            'overall_status': validation_result['overall_status'],
            'checks': validation_result['checks'],
            'warnings': validation_result['warnings'],
            'errors': validation_result['errors']
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"验证报告已保存到: {report_file}")
    
    def print_validation_summary(self, validation_result: Dict[str, Any]):
        """打印验证摘要"""
        print("\n" + "=" * 60)
        print("实体合并结果验证摘要")
        print("=" * 60)
        print(f"整体状态: {validation_result['overall_status']}")
        
        for check_name, check_result in validation_result['checks'].items():
            status_icon = {
                'pass': '✓',
                'warning': '⚠',
                'error': '✗',
                'unknown': '?'
            }.get(check_result['status'], '?')
            
            print(f"{status_icon} {check_name}: {check_result['status']}")
            
            if check_result['warnings']:
                for warning in check_result['warnings']:
                    print(f"   警告: {warning}")
        
        if validation_result['warnings']:
            print("\n整体警告:")
            for warning in validation_result['warnings']:
                print(f"  {warning}")
        
        if validation_result['errors']:
            print("\n错误:")
            for error in validation_result['errors']:
                print(f"  {error}")
        
        print("=" * 60) 